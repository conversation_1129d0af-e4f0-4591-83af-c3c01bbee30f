
import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { useLanguage } from '@/hooks/useLanguage';
import { useListings } from '@/hooks/listings/useListings';
import { useFetchListingById } from '@/hooks/listings/operations/fetchListingById';
import { useSaveListing } from '@/hooks/listings/useSaveListing';
import { Listing } from '@/hooks/listings/types';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';

export const useListingDetail = (id: string | undefined) => {
  const { t, language } = useLanguage();
  const { isAuthenticated, user } = useAuth();
  const [isLoading, setIsLoading] = useState(true);
  const { deleteListing } = useListings();
  const [listing, setListing] = useState<Listing | null>(null);
  const [sellerPhone, setSellerPhone] = useState<string>("");
  const [isDeleting, setIsDeleting] = useState(false);
  const navigate = useNavigate();
  const { fetchListingById } = useFetchListingById(setIsLoading);

  // Only initialize useSaveListing when we have an ID
  const listingId = id || '';
  const { isSaved, toggleSave } = useSaveListing(listingId);

  // Подробный отладочный лог для проверки состояния аутентификации
  useEffect(() => {
    console.log("useListingDetail hook state:", {
      isAuthenticated,
      userId: user?.id,
      listingId: id
    });
  }, [isAuthenticated, user, id]);

  const fetchData = async () => {
    if (!id) return;

    try {
      const data = await fetchListingById(id);
      if (!data) {
        toast.error(t('listings.failedToLoadListing'));
        navigate('/listings');
        return;
      }

      setListing(data);

      // Fetch seller profile to get phone number
      if (data.user_id) {
        const { data: profileData, error: profileError } = await supabase
          .from('profiles')
          .select('phone')
          .eq('id', data.user_id)
          .single();

        if (!profileError && profileData) {
          setSellerPhone(profileData.phone || "+123456789");
        } else {
          console.log("Error fetching seller profile or no phone:", profileError);
          setSellerPhone("+123456789"); // Default fallback
        }
      }
    } catch (error) {
      console.error('Error fetching listing details:', error);
      toast.error(t('listings.failedToLoadListing'));
      navigate('/listings');
    }
  };

  useEffect(() => {
    fetchData();
  }, [id]);

  const handleSaveListing = () => {
    if (!isAuthenticated) {
      navigate('/auth?returnUrl=' + encodeURIComponent(window.location.pathname));
      return;
    }
    toggleSave();
  };

  const handleDelete = async (id: string) => {
    try {
      setIsDeleting(true);
      await deleteListing(id);
      toast.success(t('listings.listingDeletedSuccessfully'));
      navigate('/listings');
    } catch (error) {
      console.error('Error deleting listing:', error);
      toast.error(t('listings.failedToDeleteListing'));
    } finally {
      setIsDeleting(false);
    }
  };

  // Определяем, является ли пользователь владельцем объявления
  const isOwner = user && listing ? user.id === listing.user_id : false;

  // Добавляем отладочный лог
  useEffect(() => {
    console.log("useListingDetail hook - isOwner:", isOwner, "isAuthenticated:", isAuthenticated);
  }, [isOwner, isAuthenticated]);

  return {
    listing,
    isLoading,
    isDeleting,
    isOwner,
    isAuthenticated,
    sellerPhone,
    isSaved,
    handleSaveListing,
    handleDelete
  };
};
