
import { Language } from '@/hooks/useLanguage';
import { translations } from '@/utils/i18n/translations';
import i18next from '@/utils/i18n';

/**
 * Attempts to find a translation using the primary language
 * @param language The language to use for translation
 * @param key The translation key
 * @returns The translated string or the key itself if not found
 */
export const getTranslation = (language: Language, key: string): string => {
  if (!key) return '';

  try {
    // First try to get the translation through i18next, which is the preferred method
    if (i18next.isInitialized) {
      const translation = i18next.t(key);
      if (translation !== key) {
        return translation;
      }
    }

    // Get translation based on current language
    const currentLang = language;
    
    // Split the key by dots to access nested properties
    const keys = key.split('.');
    
    // Get translation object for current language
    let translation = translations[currentLang]?.translation;

    if (!translation) {
      console.warn(`Translations not found for language: ${currentLang}, falling back to English`);
      translation = translations['en'].translation;
      
      if (!translation) {
        console.error('No translations found at all');
        return key;
      }
    }

    // Handle nested keys (section.key format)
    if (key.includes('.')) {
      const [section, subKey] = key.split('.');
      if (translation && typeof translation === 'object' && section in translation) {
        const sectionTranslation = (translation as any)[section];
        if (sectionTranslation && typeof sectionTranslation === 'object' && subKey in sectionTranslation) {
          return sectionTranslation[subKey];
        }
      }
    }

    // Try to find the key in any section if not using dot notation
    if (!key.includes('.')) {
      const sections = ['ui', 'listings', 'auth', 'profile', 'admin', 'system', 'car', 'common'];
      for (const section of sections) {
        if (translation && typeof translation === 'object' && section in translation) {
          const sectionTranslation = (translation as any)[section];
          if (sectionTranslation && typeof sectionTranslation === 'object' && key in sectionTranslation) {
            return sectionTranslation[key];
          }
        }
      }
    }

    // Standard nested key lookup
    let result = translation;
    for (const k of keys) {
      if (result && typeof result === 'object' && k in result) {
        result = result[k];
      } else {
        result = undefined;
        break;
      }
    }

    if (typeof result === 'string') {
      return result;
    }

    return key;
  } catch (error) {
    console.error(`Error translating key: ${key}`, error);
    return key;
  }
};

/**
 * Attempts fallback translation when primary language translation fails
 * @param fallbackLang The fallback language to use
 * @param key The translation key
 * @returns The translated string or the key itself if not found
 */
export const getFallbackTranslation = (fallbackLang: Language, key: string): string => {
  try {
    // First try i18next with the fallback language
    if (i18next.isInitialized) {
      const savedLang = i18next.language;
      i18next.changeLanguage(fallbackLang);
      const translation = i18next.t(key);
      i18next.changeLanguage(savedLang);
      if (translation !== key) {
        return translation;
      }
    }
    
    const fallbackTranslation = translations[fallbackLang]?.translation;
    
    if (!fallbackTranslation) {
      return key;
    }
    
    // Handle nested keys for fallback
    if (key.includes('.')) {
      const [section, subKey] = key.split('.');
      if (fallbackTranslation && typeof fallbackTranslation === 'object' && section in fallbackTranslation) {
        const sectionTranslation = (fallbackTranslation as any)[section];
        if (sectionTranslation && typeof sectionTranslation === 'object' && subKey in sectionTranslation) {
          return sectionTranslation[subKey];
        }
      }
    }
    
    // Try sections for fallback
    if (!key.includes('.')) {
      const sections = ['ui', 'listings', 'auth', 'profile', 'admin', 'system', 'car', 'common'];
      for (const section of sections) {
        if (fallbackTranslation && typeof fallbackTranslation === 'object' && section in fallbackTranslation) {
          const sectionTranslation = (fallbackTranslation as any)[section];
          if (sectionTranslation && typeof sectionTranslation === 'object' && key in sectionTranslation) {
            return sectionTranslation[key];
          }
        }
      }
    }
    
    // Standard nested key lookup for fallback
    const keys = key.split('.');
    let result = fallbackTranslation;
    for (const k of keys) {
      if (result && typeof result === 'object' && k in result) {
        result = result[k];
      } else {
        result = undefined;
        break;
      }
    }
    
    if (typeof result === 'string') {
      return result;
    }
    
    return key;
  } catch (error) {
    console.error(`Error in fallback translation for key: ${key}`, error);
    return key;
  }
};

/**
 * Translates a key to the specified language with fallback to English
 * Direct wrapper around i18next.t with additional fallback logic
 * @param language Primary language to use
 * @param key Translation key
 * @returns Translated string
 */
export const translateKey = (language: Language, key: string): string => {
  try {
    // First attempt to use i18next directly
    if (i18next.isInitialized) {
      const currentLang = i18next.language;
      
      // Only change language if needed
      if (currentLang !== language) {
        i18next.changeLanguage(language);
      }
      
      const translation = i18next.t(key);
      
      // If i18next returns the key itself, it means translation failed
      if (translation !== key) {
        return translation;
      }
    }
    
    // Fall back to manual lookup if i18next failed
    const translation = getTranslation(language, key);
    
    // If translation is the same as key, try fallback to English
    if (translation === key && language !== 'en') {
      return getFallbackTranslation('en', key);
    }
    
    return translation;
  } catch (error) {
    console.error(`Translation error for key "${key}":`, error);
    // Last resort: return key with a marker to indicate translation failure
    return key;
  }
};
