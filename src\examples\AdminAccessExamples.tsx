import React from 'react';
import { AdminGuard } from '@/components/auth/AdminGuard';
import { ProtectedAction, ProtectedContent } from '@/components/auth/ProtectedAction';
import { useAdminAccess, useAdminPermissions } from '@/hooks/useAdminAccess';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Shield, Users, Settings, Trash2 } from 'lucide-react';

/**
 * Примеры использования системы контроля доступа для административной панели
 * Этот файл демонстрирует различные способы защиты UI элементов и действий
 */

// Пример 1: Простая защита административного контента
export const SimpleAdminGuardExample: React.FC = () => {
  return (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold">Пример 1: Простая защита контента</h3>
      
      {/* Этот контент будет виден только администраторам */}
      <AdminGuard>
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Shield className="h-5 w-5" />
              Административная панель
            </CardTitle>
            <CardDescription>
              Этот контент виден только пользователям с правами администратора
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p>Секретная информация для администраторов</p>
          </CardContent>
        </Card>
      </AdminGuard>
      
      {/* Этот контент виден всем */}
      <Card>
        <CardHeader>
          <CardTitle>Публичный контент</CardTitle>
          <CardDescription>
            Этот контент виден всем пользователям
          </CardDescription>
        </CardHeader>
        <CardContent>
          <p>Общедоступная информация</p>
        </CardContent>
      </Card>
    </div>
  );
};

// Пример 2: Защищенные действия с подтверждением
export const ProtectedActionsExample: React.FC = () => {
  const handleDeleteUser = async () => {
    // Симуляция удаления пользователя
    await new Promise(resolve => setTimeout(resolve, 1000));
    console.log('Пользователь удален');
  };

  const handleUpdateSettings = async () => {
    // Симуляция обновления настроек
    await new Promise(resolve => setTimeout(resolve, 500));
    console.log('Настройки обновлены');
  };

  return (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold">Пример 2: Защищенные действия</h3>
      
      <div className="flex gap-4">
        {/* Действие, требующее административных прав */}
        <ProtectedAction
          action={handleDeleteUser}
          requireAdmin={true}
          confirmMessage="Вы уверены, что хотите удалить этого пользователя?"
          successMessage="Пользователь успешно удален"
          errorMessage="Ошибка при удалении пользователя"
          variant="destructive"
        >
          <Trash2 className="h-4 w-4 mr-2" />
          Удалить пользователя
        </ProtectedAction>

        {/* Действие, требующее только аутентификации */}
        <ProtectedAction
          action={handleUpdateSettings}
          requireAuth={true}
          successMessage="Настройки обновлены"
          variant="outline"
        >
          <Settings className="h-4 w-4 mr-2" />
          Обновить настройки
        </ProtectedAction>
      </div>
    </div>
  );
};

// Пример 3: Условный рендеринг с использованием хуков
export const ConditionalRenderingExample: React.FC = () => {
  const { isAdmin, isLoading } = useAdminAccess();
  const { permissions, hasPermission } = useAdminPermissions();

  if (isLoading) {
    return <div>Проверка прав доступа...</div>;
  }

  return (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold">Пример 3: Условный рендеринг</h3>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* Карточка для администраторов */}
        {isAdmin && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5" />
                Управление пользователями
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p>Вы можете управлять пользователями системы</p>
              <div className="mt-4 space-y-2">
                <p className="text-sm text-muted-foreground">Ваши разрешения:</p>
                <ul className="text-sm space-y-1">
                  {permissions.canManageUsers && <li>✓ Управление пользователями</li>}
                  {permissions.canManageListings && <li>✓ Управление объявлениями</li>}
                  {permissions.canViewReports && <li>✓ Просмотр отчетов</li>}
                  {permissions.canManageSettings && <li>✓ Управление настройками</li>}
                </ul>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Карточка для обычных пользователей */}
        {!isAdmin && (
          <Card>
            <CardHeader>
              <CardTitle>Пользовательская панель</CardTitle>
            </CardHeader>
            <CardContent>
              <p>Добро пожаловать! Вы можете управлять своими объявлениями.</p>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
};

// Пример 4: Защищенный контент с fallback
export const ProtectedContentExample: React.FC = () => {
  return (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold">Пример 4: Защищенный контент с fallback</h3>
      
      <ProtectedContent
        requireAdmin={true}
        showAccessDenied={true}
        fallback={
          <Card>
            <CardContent className="pt-6">
              <p className="text-center text-muted-foreground">
                Загрузка административной панели...
              </p>
            </CardContent>
          </Card>
        }
      >
        <Card>
          <CardHeader>
            <CardTitle>Административная статистика</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold">1,234</div>
                <div className="text-sm text-muted-foreground">Пользователей</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold">5,678</div>
                <div className="text-sm text-muted-foreground">Объявлений</div>
              </div>
            </div>
          </CardContent>
        </Card>
      </ProtectedContent>
    </div>
  );
};

// Пример 5: Комплексная административная панель
export const CompleteAdminPanelExample: React.FC = () => {
  return (
    <AdminGuard fallback={
      <div className="text-center p-8">
        <Shield className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
        <h3 className="text-lg font-semibold mb-2">Доступ ограничен</h3>
        <p className="text-muted-foreground">
          Эта страница доступна только администраторам
        </p>
      </div>
    }>
      <div className="space-y-6">
        <h2 className="text-2xl font-bold">Административная панель</h2>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <SimpleAdminGuardExample />
          <ProtectedActionsExample />
          <ConditionalRenderingExample />
        </div>
        
        <ProtectedContentExample />
      </div>
    </AdminGuard>
  );
};

export default {
  SimpleAdminGuardExample,
  ProtectedActionsExample,
  ConditionalRenderingExample,
  ProtectedContentExample,
  CompleteAdminPanelExample,
};
