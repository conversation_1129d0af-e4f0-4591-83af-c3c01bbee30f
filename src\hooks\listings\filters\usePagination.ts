
import { useState, useEffect } from 'react';
import { useSearchParams } from 'react-router-dom';

export const usePagination = (totalItems: number, itemsPerPage: number = 12) => {
  const [searchParams, setSearchParams] = useSearchParams();
  const pageFromUrl = searchParams.get('page');
  
  const [currentPage, setCurrentPage] = useState(pageFromUrl ? parseInt(pageFromUrl) : 1);
  const [totalPages, setTotalPages] = useState(Math.ceil(totalItems / itemsPerPage) || 1);
  
  // Update totalPages when totalItems or itemsPerPage changes
  useEffect(() => {
    const total = Math.ceil(totalItems / itemsPerPage);
    setTotalPages(total || 1); // Ensure at least 1 page
    
    // Reset to page 1 if current page is greater than total pages
    if (currentPage > total && total > 0) {
      setCurrentPage(1);
    }
  }, [totalItems, itemsPerPage, currentPage]);
  
  // Update when pageFromUrl changes
  useEffect(() => {
    if (pageFromUrl) {
      setCurrentPage(parseInt(pageFromUrl));
    }
  }, [pageFromUrl]);

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    
    // Scroll to top of listings section
    const container = document.querySelector('.container');
    if (container) {
      window.scrollTo({
        top: container.getBoundingClientRect().top + window.scrollY,
        behavior: 'smooth'
      });
    }
  };
  
  const getPaginatedItems = <T>(items: T[]): T[] => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    return items.slice(startIndex, endIndex);
  };
  
  return {
    currentPage,
    setCurrentPage,
    totalPages,
    handlePageChange,
    getPaginatedItems
  };
};
