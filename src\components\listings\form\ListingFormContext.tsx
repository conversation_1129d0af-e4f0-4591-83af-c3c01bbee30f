
import React, { createContext, useContext } from 'react';
import { FormContextType } from './types';

const FormContext = createContext<FormContextType | undefined>(undefined);

export const FormProvider = FormContext.Provider;

export const useFormContext = (): FormContextType => {
  const context = useContext(FormContext);
  if (!context) {
    throw new Error('useFormContext must be used within a FormProvider');
  }
  return context;
};
