
import { serve } from "https://deno.land/std@0.190.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2.33.1";

const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers":
    "authorization, x-client-info, apikey, content-type",
};

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === "OPTIONS") {
    return new Response("ok", { headers: corsHeaders });
  }

  try {
    const supabaseUrl = Deno.env.get("SUPABASE_URL") || "";
    const supabaseServiceKey = Deno.env.get("SUPABASE_SERVICE_ROLE_KEY") || "";

    const supabase = createClient(supabaseUrl, supabaseServiceKey);

    // Get all saved searches with notifications enabled
    const { data: searches, error: searchesError } = await supabase
      .from("saved_searches")
      .select("*")
      .eq("notify", true);

    if (searchesError) throw searchesError;

    console.log(`Processing ${searches.length} saved searches`);
    
    const lastCheckTime = getLastCheckTime();
    
    // Find new listings since last check
    const { data: newListings, error: listingsError } = await supabase
      .from("listings")
      .select("*")
      .gt("created_at", lastCheckTime);
      
    if (listingsError) throw listingsError;
    
    console.log(`Found ${newListings.length} new listings`);
    
    // For each saved search, find matching new listings and notify users
    const notifications = [];
    
    for (const search of searches) {
      // Filter listings based on search criteria
      const matchingListings = filterListingsForSearch(newListings, search);
      
      if (matchingListings.length > 0) {
        const { data: userProfile, error: profileError } = await supabase
          .from("profiles")
          .select("*")
          .eq("id", search.user_id)
          .single();
          
        if (profileError) {
          console.error(`Error fetching profile for user ${search.user_id}:`, profileError);
          continue;
        }
        
        // Create notification entry
        const notification = {
          user_id: search.user_id,
          search_id: search.id,
          message: `Found ${matchingListings.length} new listings for your saved search "${search.name}"`,
          listing_ids: matchingListings.map(listing => listing.id),
          search_name: search.name,
          created_at: new Date().toISOString(),
          read: false
        };
        
        notifications.push(notification);
      }
    }
    
    // Insert notifications if any
    if (notifications.length > 0) {
      const { error: notificationError } = await supabase
        .from("search_notifications")
        .insert(notifications);
        
      if (notificationError) throw notificationError;
      
      console.log(`Created ${notifications.length} notifications`);
    }
    
    // Update the last check time
    setLastCheckTime(new Date().toISOString());
    
    return new Response(
      JSON.stringify({ 
        success: true,
        searches_processed: searches.length,
        new_listings: newListings.length,
        notifications_created: notifications.length
      }),
      {
        headers: { ...corsHeaders, "Content-Type": "application/json" },
        status: 200,
      }
    );
  } catch (error) {
    console.error("Error processing saved searches:", error);
    
    return new Response(
      JSON.stringify({ error: error.message }),
      {
        headers: { ...corsHeaders, "Content-Type": "application/json" },
        status: 500,
      }
    );
  }
});

// Helper function to filter listings based on search criteria
function filterListingsForSearch(listings, search) {
  let filtered = [...listings];
  
  // Filter by search term
  if (search.search_term && search.search_term.trim() !== '') {
    const lowercaseSearch = search.search_term.toLowerCase();
    filtered = filtered.filter(
      listing => 
        listing.title.toLowerCase().includes(lowercaseSearch) || 
        listing.make.toLowerCase().includes(lowercaseSearch) || 
        listing.model.toLowerCase().includes(lowercaseSearch) || 
        listing.location.toLowerCase().includes(lowercaseSearch)
    );
  }
  
  // Apply any filters from the search
  const filters = search.filters || {};
  
  Object.entries(filters).forEach(([key, value]) => {
    if (value !== undefined && value !== null && value !== '') {
      // Handle different filter types
      if (key === 'make' && value !== 'all') {
        filtered = filtered.filter(listing => listing.make === value);
      } else if (key === 'minPrice') {
        filtered = filtered.filter(listing => Number(listing.price) >= Number(value));
      } else if (key === 'maxPrice') {
        filtered = filtered.filter(listing => Number(listing.price) <= Number(value));
      } else if (key === 'minYear') {
        filtered = filtered.filter(listing => listing.year >= Number(value));
      } else if (key === 'maxYear') {
        filtered = filtered.filter(listing => listing.year <= Number(value));
      }
    }
  });
  
  return filtered;
}

// Simple KV store for last check time
function getLastCheckTime() {
  try {
    // Default to 1 hour ago if no last check time is found
    const oneHourAgo = new Date();
    oneHourAgo.setHours(oneHourAgo.getHours() - 1);
    return Deno.env.get("LAST_CHECK_TIME") || oneHourAgo.toISOString();
  } catch (err) {
    console.error("Error getting last check time:", err);
    const oneHourAgo = new Date();
    oneHourAgo.setHours(oneHourAgo.getHours() - 1);
    return oneHourAgo.toISOString();
  }
}

function setLastCheckTime(time) {
  try {
    Deno.env.set("LAST_CHECK_TIME", time);
  } catch (err) {
    console.error("Error setting last check time:", err);
  }
}
