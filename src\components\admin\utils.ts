
import { Listing } from '@/hooks/listings/types';
import { UserData } from './users/UsersData';

export const paginateListings = (
  listings: Listing[],
  currentPage: number,
  itemsPerPage: number
) => {
  const totalPages = Math.max(1, Math.ceil(listings.length / itemsPerPage));
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentItems = listings.slice(indexOfFirstItem, indexOfLastItem);
  
  return {
    currentItems,
    totalPages,
  };
};

export const paginateUsers = (
  users: UserData[],
  currentPage: number,
  itemsPerPage: number
) => {
  const totalPages = Math.max(1, Math.ceil(users.length / itemsPerPage));
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentItems = users.slice(indexOfFirstItem, indexOfLastItem);
  
  return {
    currentItems,
    totalPages,
  };
};

export const paginateData = <T>(
  data: T[],
  currentPage: number,
  itemsPerPage: number
) => {
  const totalPages = Math.max(1, Math.ceil(data.length / itemsPerPage));
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentItems = data.slice(indexOfFirstItem, indexOfLastItem);
  
  return {
    currentItems,
    totalPages,
  };
};
