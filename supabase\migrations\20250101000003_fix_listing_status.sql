-- Fix listing status values and add default
-- This migration ensures all listings have proper status values

-- Update existing listings without status to have 'approved' status
UPDATE listings 
SET status = 'approved' 
WHERE status IS NULL OR status = '';

-- Add a check constraint to ensure status has valid values
ALTER TABLE listings 
ADD CONSTRAINT listings_status_check 
CHECK (status IN ('pending', 'approved', 'rejected', 'blocked', 'active'));

-- Set default status for new listings
ALTER TABLE listings 
ALTER COLUMN status SET DEFAULT 'pending';

-- Create an index on status for better query performance
CREATE INDEX IF NOT EXISTS idx_listings_status ON listings(status);

-- Create an index on status and created_at for admin queries
CREATE INDEX IF NOT EXISTS idx_listings_status_created_at ON listings(status, created_at DESC);

-- Add some sample data with different statuses if no data exists
DO $$
BEGIN
  -- Check if we have any listings
  IF NOT EXISTS (SELECT 1 FROM listings LIMIT 1) THEN
    -- Insert sample listings with different statuses
    INSERT INTO listings (
      title, description, make, model, year, price, location, 
      featured, status, user_id, image_urls
    ) VALUES 
    (
      'BMW X5 Engine 3.0L Diesel 2018',
      'Complete engine assembly from BMW X5. Excellent condition, low mileage.',
      'BMW', 'X5', 2018, 3500, 'Los Angeles, CA',
      true, 'approved', 'system',
      ARRAY['https://images.unsplash.com/photo-1619642751034-765dfdf7c58e?auto=format&fit=crop&q=80&w=1974&ixlib=rb-4.0.3']
    ),
    (
      'Toyota Camry Transmission 2020',
      'Automatic transmission from Toyota Camry 2020. Perfect working condition.',
      'Toyota', 'Camry', 2020, 1200, 'New York, NY',
      false, 'pending',  'system',
      ARRAY['https://images.unsplash.com/photo-1617814076367-b759c7d7e738?auto=format&fit=crop&q=80&w=2574&ixlib=rb-4.0.3']
    ),
    (
      'Mercedes E-Class Front Bumper 2019',
      'Original front bumper from Mercedes E-Class 2019. Minor scratches.',
      'Mercedes-Benz', 'E-Class', 2019, 850, 'Chicago, IL',
      true, 'approved', 'system',
      ARRAY['https://images.unsplash.com/photo-1533473359331-0135ef1b58bf?auto=format&fit=crop&q=80&w=2670&ixlib=rb-4.0.3']
    ),
    (
      'Ford F-150 Tailgate 2021',
      'Original tailgate from Ford F-150 2021. Excellent condition.',
      'Ford', 'F-150', 2021, 650, 'Dallas, TX',
      false, 'pending', 'system',
      ARRAY['https://images.unsplash.com/photo-1558618666-fcd25c85cd64?auto=format&fit=crop&q=80&w=2670&ixlib=rb-4.0.3']
    ),
    (
      'Honda Civic Headlights Set 2019',
      'Complete LED headlights set from Honda Civic 2019.',
      'Honda', 'Civic', 2019, 450, 'Miami, FL',
      false, 'approved', 'system',
      ARRAY['https://images.unsplash.com/photo-1552519507-da3b142c6e3d?auto=format&fit=crop&q=80&w=2670&ixlib=rb-4.0.3']
    );
    
    RAISE NOTICE 'Sample listings with different statuses have been created';
  ELSE
    RAISE NOTICE 'Listings already exist, skipping sample data creation';
  END IF;
END $$;
