
import React from 'react';
import { useAuth } from '@/contexts/AuthContext';
import LanguageSelector from './LanguageSelector';
import ThemeToggle from './ThemeToggle';
import { Button } from '@/components/ui/button';
import AuthButtons from './AuthButtons';
import SearchNotifications from '@/components/notifications/SearchNotifications';
import MessageNotifications from '@/components/notifications/MessageNotifications';

const DesktopActions: React.FC = () => {
  const { isAuthenticated } = useAuth();

  return (
    <div className="flex items-center gap-2">
      {isAuthenticated && (
        <>
          <SearchNotifications />
          <MessageNotifications />
        </>
      )}
      <LanguageSelector />
      <ThemeToggle />
      <AuthButtons />
    </div>
  );
};

export default DesktopActions;
