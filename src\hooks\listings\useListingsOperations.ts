
import { Listing } from './types';
import { useCreateListing } from './operations/createListing';
import { useUpdateListing } from './operations/updateListing';
import { useDeleteListing } from './operations/deleteListing';
import { useFeatureListing } from './operations/featureListing';
import { useUpdateListingStatus } from './operations/updateListingStatus';

export const useListingsOperations = (
  setIsLoading: React.Dispatch<React.SetStateAction<boolean>>,
  setListings: React.Dispatch<React.SetStateAction<Listing[]>>,
  setFeaturedListings: React.Dispatch<React.SetStateAction<Listing[]>>,
  fetchListings: () => Promise<void>
) => {
  const { createListing } = useCreateListing(
    setIsLoading,
    setListings,
    setFeaturedListings,
    fetchListings
  );

  const { updateListing } = useUpdateListing(
    setIsLoading,
    setListings,
    setFeaturedListings
  );

  const { deleteListing } = useDeleteListing(
    setIsLoading,
    setListings,
    setFeaturedListings
  );

  const { featureListing } = useFeatureListing(
    setIsLoading,
    setListings,
    setFeaturedListings
  );
  
  const { setVipStatus, setPaidStatus, updateListingStatus } = useUpdateListingStatus(
    setIsLoading,
    setListings,
    setFeaturedListings
  );

  return {
    createListing,
    updateListing,
    deleteListing,
    featureListing,
    setVipStatus,
    setPaidStatus,
    updateListingStatus
  };
};
