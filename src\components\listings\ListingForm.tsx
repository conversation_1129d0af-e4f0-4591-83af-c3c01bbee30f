
import React from 'react';
import { Card } from '@/components/ui/card';
import { Form } from '@/components/ui/form';
import { useListingFormState } from './form/useListingFormState';
import { useListingFormSubmit } from './form/useListingFormSubmit';
import { ListingFormProps } from './form/types';
import { FormProvider } from './form/ListingFormContext';

// Import form sections
import BasicInfoForm from './form/BasicInfoForm';
import VehicleDetailsForm from './form/VehicleDetailsForm';
import LocationForm from './form/LocationForm';
import FormFooter from './form/FormFooter';
import AdditionalDetailsForm from './form/AdditionalDetailsForm';
import ImagesFormSection from './form/ImagesFormSection';

const ListingForm: React.FC<ListingFormProps> = ({ 
  onCancel, 
  existingData, 
  isEdit = false 
}) => {
  const { form, customValues } = useListingFormState(existingData);
  const { isSubmitting, handleSubmit } = useListingFormSubmit(isEdit, existingData);
  
  const onSubmit = async (formData: any) => {
    await handleSubmit(
      formData,
      customValues.customMake,
      customValues.customModel,
      customValues.customLocation
    );
  };
  
  const formContext = {
    form,
    ...customValues
  };
  
  return (
    <Card className="p-6">
      <FormProvider value={formContext}>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <BasicInfoForm />
            <VehicleDetailsForm />
            <AdditionalDetailsForm />
            <LocationForm />
            <ImagesFormSection />
            <FormFooter 
              isSubmitting={isSubmitting}
              onCancel={onCancel}
              isEdit={isEdit}
            />
          </form>
        </Form>
      </FormProvider>
    </Card>
  );
};

export default ListingForm;
