
import React, { memo, useState, useEffect } from 'react';
import { useLanguage } from '@/hooks/useLanguage';
import ListingCard from '@/components/ListingCard';
import ListingListCard from '@/components/ListingListCard';
import { Button } from '@/components/ui/button';
import { Link } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { Listing } from '@/hooks/listings/types';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/components/ui/alert-dialog';
import { toast } from 'sonner';
import { useListings } from '@/hooks/listings/useListings';
import { Trash2 } from 'lucide-react';
import { useIsMobile } from '@/hooks/use-mobile';
import { GridViewType } from './GridViewToggle';

interface ListingsGridProps {
  listings: Listing[];
  isLoading: boolean;
  gridView?: GridViewType;
}

const ListingsGrid: React.FC<ListingsGridProps> = ({ listings, isLoading, gridView = 'multiple' }) => {
  const { t } = useLanguage();
  const { isAuthenticated, user } = useAuth();
  const { deleteListing } = useListings();
  const isMobile = useIsMobile();

  const renderSkeletons = () => {
    return Array(8).fill(0).map((_, index) => (
      <div key={index} className="bg-card rounded-xl overflow-hidden shadow-subtle animate-pulse">
        <div className="aspect-[3/2] bg-muted"></div>
        <div className="p-4 space-y-3">
          <div className="h-5 bg-muted rounded-md w-4/5"></div>
          <div className="h-4 bg-muted rounded-md w-1/2"></div>
          <div className="h-4 bg-muted rounded-md w-2/3"></div>
        </div>
      </div>
    ));
  };

  const handleDeleteListing = async (id: string) => {
    try {
      toast(t('listings.deleting'));
      await deleteListing(id);
      toast.success(t('listings.listingDeletedSuccessfully'));
    } catch (error) {
      console.error("Error deleting listing:", error);
      toast.error(t('listings.failedToDeleteListing'));
    }
  };

  // Define grid classes based on selected view
  const getGridClasses = () => {
    if (gridView === 'list') {
      return 'space-y-4';
    }
    if (gridView === 'single') {
      return 'grid grid-cols-1 gap-6';
    }
    return 'grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6';
  };

  if (isLoading) {
    return (
      <div className={getGridClasses()}>
        {renderSkeletons()}
      </div>
    );
  }

  if (listings.length === 0) {
    return (
      <div className="text-center py-10">
        <p className="text-muted-foreground">{t('listings.noListingsFound')}</p>
        {isAuthenticated && (
          <Button asChild className="mt-4">
            <Link to="/create-listing">
              {t('listings.createListing')}
            </Link>
          </Button>
        )}
      </div>
    );
  }

  return (
    <div className={getGridClasses()}>
        {listings.map((listing) => (
          <div key={listing.id} className="relative group">
            {gridView === 'list' ? (
              <ListingListCard
                id={listing.id}
                title={listing.title}
                year={listing.year}
                make={listing.make}
                model={listing.model}
                price={Number(listing.price)}
                location={listing.location}
                imageUrl={listing.image_urls && listing.image_urls.length > 0
                  ? listing.image_urls[0]
                  : 'https://images.unsplash.com/photo-1533473359331-0135ef1b58bf?auto=format&fit=crop&q=80&w=2670&ixlib=rb-4.0.3'}
                featured={listing.featured}
                createdAt={listing.created_at}
                className="animate-fade-in"
              />
            ) : (
              <ListingCard
                id={listing.id}
                title={listing.title}
                year={listing.year}
                make={listing.make}
                model={listing.model}
                price={Number(listing.price)}
                location={listing.location}
                imageUrl={listing.image_urls && listing.image_urls.length > 0
                  ? listing.image_urls[0]
                  : 'https://images.unsplash.com/photo-1533473359331-0135ef1b58bf?auto=format&fit=crop&q=80&w=2670&ixlib=rb-4.0.3'}
                featured={listing.featured}
                createdAt={listing.created_at}
                className="animate-fade-in"
              />
            )}

            {isAuthenticated && user && user.id === listing.user_id && (
              <div className="absolute top-2 left-2 opacity-0 group-hover:opacity-100 transition-opacity">
                <AlertDialog>
                  <AlertDialogTrigger asChild>
                    <Button variant="destructive" size="icon" className="h-8 w-8 rounded-full">
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </AlertDialogTrigger>
                  <AlertDialogContent>
                    <AlertDialogHeader>
                      <AlertDialogTitle>{t('listings.confirmDelete')}</AlertDialogTitle>
                      <AlertDialogDescription>
                        {t('listings.deleteListingConfirmation')}
                      </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                      <AlertDialogCancel>{t('listings.cancel')}</AlertDialogCancel>
                      <AlertDialogAction
                        onClick={() => handleDeleteListing(listing.id)}
                        className="bg-destructive hover:bg-destructive/90"
                      >
                        {t('listings.delete')}
                      </AlertDialogAction>
                    </AlertDialogFooter>
                  </AlertDialogContent>
                </AlertDialog>
              </div>
            )}
          </div>
        ))}
    </div>
  );
};

// Memoize the component to prevent unnecessary re-renders
export default memo(ListingsGrid);
