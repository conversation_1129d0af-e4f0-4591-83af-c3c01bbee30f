
import React from 'react';
import { useListings } from '@/hooks/useListings';
import { toast } from 'sonner';
import ListingSearch from './listings/ListingSearch';
import ListingFilters from './listings/ListingFilters';
import ListingTable from './listings/ListingTable';
import AdminListingsPagination from './listings/AdminListingsPagination';
import ItemsPerPageSelector from './listings/ItemsPerPageSelector';
import EmptyState from './listings/EmptyState';
import LoadingState from './listings/LoadingState';
import { useLanguage } from '@/hooks/useLanguage';
import { useAdminListingsPagination } from '@/hooks/useAdminListingsPagination';

const AdminListings: React.FC = () => {
  const { t } = useLanguage();
  const { featureListing, deleteListing, setVipStatus, setPaidStatus } = useListings();

  // Use the new pagination hook
  const {
    currentPage,
    itemsPerPage,
    totalItems,
    totalPages,
    listings,
    isLoading,
    searchTerm,
    featuredFilter,
    vipFilter,
    paidFilter,
    handlePageChange,
    handleItemsPerPageChange,
    handleSearch,
    handleFilterChange,
    clearFilters,
    refresh,
  } = useAdminListingsPagination();



  const handleFeatureToggle = async (id: string, currentStatus: boolean) => {
    try {
      await featureListing(id, !currentStatus);
      toast.success(currentStatus
        ? t('admin.removedFromFeatured')
        : t('admin.addedToFeatured'), {
          description: currentStatus
            ? t('admin.removedFromFeaturedDesc')
            : t('admin.addedToFeaturedDesc')
        }
      );
    } catch (error) {
      console.error('Error toggling feature status:', error);
      toast.error(t('admin.featureUpdateError'), {
        description: t('admin.featureUpdateErrorDesc')
      });
    }
  };

  const handleVipStatusToggle = async (id: string, currentStatus: boolean) => {
    try {
      await setVipStatus(id, !currentStatus);
      toast.success(currentStatus
        ? t('admin.vipStatusRemoved')
        : t('admin.vipStatusAdded'), {
          description: currentStatus
            ? t('admin.vipStatusRemovedDesc')
            : t('admin.vipStatusAddedDesc')
        }
      );
    } catch (error) {
      console.error('Error toggling VIP status:', error);
      toast.error(t('admin.vipStatusUpdateError'), {
        description: t('admin.vipStatusUpdateErrorDesc')
      });
    }
  };

  const handlePaidStatusToggle = async (id: string, currentStatus: boolean) => {
    try {
      await setPaidStatus(id, !currentStatus);
      toast.success(t('admin.paidStatusUpdated'), {
        description: t('admin.paidStatusUpdatedDesc')
      });
    } catch (error) {
      console.error('Error toggling paid status:', error);
      toast.error(t('admin.paidStatusUpdateError'), {
        description: t('admin.paidStatusUpdateErrorDesc')
      });
    }
  };

  const handleDeleteListing = async (id: string) => {
    try {
      await deleteListing(id);
      toast.success(t('admin.listingDeletedSuccess'), {
        description: t('admin.listingDeletedSuccessDesc')
      });
    } catch (error) {
      console.error('Error deleting listing:', error);
      toast.error(t('admin.listingDeleteError'), {
        description: t('admin.listingDeleteErrorDesc')
      });
    }
  };

  const handleBlockListing = async (id: string, isBlocked: boolean) => {
    try {
      // For this MVP, we'll just toggle featured off as a form of "blocking"
      // In a real implementation, you would probably have a separate "blocked" status field
      if (!isBlocked) {
        await featureListing(id, false);
      }
      toast.success(t('admin.listingBlocked'), {
        description: t('admin.listingBlockedDesc')
      });
    } catch (error) {
      console.error('Error blocking listing:', error);
      toast.error(t('admin.blockError'), {
        description: t('admin.blockErrorDesc')
      });
    }
  };

  const handleRefresh = async () => {
    toast.loading(t('admin.refreshingListings'));
    await refresh();
    toast.success(t('admin.listingsRefreshed'));
  };

  return (
    <div className="space-y-6">
      {/* Header with search and controls */}
      <div className="flex flex-col lg:flex-row gap-4 justify-between">
        <div className="flex flex-col sm:flex-row gap-4 flex-1">
          <ListingSearch
            searchTerm={searchTerm}
            setSearchTerm={handleSearch}
          />

          <ListingFilters
            featureFilter={featuredFilter}
            setFeatureFilter={(value) => handleFilterChange('featured', value)}
            vipFilter={vipFilter}
            setVipFilter={(value) => handleFilterChange('vipStatus', value)}
            paidFilter={paidFilter}
            setPaidFilter={(value) => handleFilterChange('paidStatus', value)}
            onRefresh={handleRefresh}
          />
        </div>

        {/* Items per page selector */}
        <ItemsPerPageSelector
          value={itemsPerPage}
          onChange={handleItemsPerPageChange}
          className="self-start"
        />
      </div>

      {/* Loading state */}
      {isLoading && (
        <div className="text-center py-8">
          <div className="text-muted-foreground">{t('admin.loadingListings')}</div>
        </div>
      )}

      {/* Content */}
      {!isLoading && listings.length > 0 ? (
        <>
          <ListingTable
            listings={listings}
            onFeatureToggle={handleFeatureToggle}
            onVipStatusToggle={handleVipStatusToggle}
            onPaidStatusToggle={handlePaidStatusToggle}
            onDelete={handleDeleteListing}
            onBlockToggle={handleBlockListing}
          />

          <AdminListingsPagination
            currentPage={currentPage}
            totalPages={totalPages}
            totalItems={totalItems}
            itemsPerPage={itemsPerPage}
            onPageChange={handlePageChange}
            isLoading={isLoading}
          />
        </>
      ) : !isLoading ? (
        <EmptyState onClearFilters={clearFilters} />
      ) : null}
    </div>
  );
};

export default AdminListings;
