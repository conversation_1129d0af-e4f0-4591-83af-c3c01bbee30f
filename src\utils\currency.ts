/**
 * Currency formatting utilities
 */

export const formatCurrency = (amount: number, currency: string = 'USD'): string => {
  try {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  } catch (error) {
    // Fallback if currency is not supported
    return `$${amount.toLocaleString()}`;
  }
};

export const formatPrice = (price: number): string => {
  return formatCurrency(price);
};

export const parseCurrency = (currencyString: string): number => {
  // Remove currency symbols and parse as number
  const cleanString = currencyString.replace(/[^0-9.-]+/g, '');
  return parseFloat(cleanString) || 0;
};
