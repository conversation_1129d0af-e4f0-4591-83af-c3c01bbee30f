
import React from 'react';
import { useLanguage } from '@/hooks/useLanguage';
import { Link } from 'react-router-dom';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Star, Trash2, Edit, Ban, BadgeCheck, BadgeDollarSign } from 'lucide-react';

interface Listing {
  id: string;
  title: string;
  make: string;
  model: string;
  price: number;
  featured: boolean;
  vip_status?: boolean;
  paid_status?: boolean;
  created_at: string;
}

interface ListingTableProps {
  listings: Listing[];
  onFeatureToggle: (id: string, currentStatus: boolean) => Promise<void>;
  onVipStatusToggle?: (id: string, currentStatus: boolean) => Promise<void>;
  onPaidStatusToggle?: (id: string, currentStatus: boolean) => Promise<void>;
  onDelete: (id: string) => Promise<void>;
  onBlockToggle?: (id: string, isBlocked: boolean) => Promise<void>;
}

const ListingTable: React.FC<ListingTableProps> = ({
  listings,
  onFeatureToggle,
  onVipStatusToggle,
  onPaidStatusToggle,
  onDelete,
  onBlockToggle
}) => {
  const { t } = useLanguage();

  // Debug: Log when ListingTable renders
  console.log('ListingTable render:', {
    listingsCount: listings.length,
    listings: listings.slice(0, 2), // Show first 2 for debugging
    hasListings: listings && listings.length > 0,
    listingsType: typeof listings,
    isArray: Array.isArray(listings)
  });

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
  };

  return (
    <div className="rounded-md border">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>{t('admin.listingTitle')}</TableHead>
            <TableHead>{t('make')}</TableHead>
            <TableHead>{t('model')}</TableHead>
            <TableHead>{t('price')}</TableHead>
            <TableHead>{t('admin.featuredStatus')}</TableHead>
            <TableHead>{t('admin.vipStatus')}</TableHead>
            <TableHead>{t('admin.paidStatus')}</TableHead>
            <TableHead>{t('admin.createdDate')}</TableHead>
            <TableHead>{t('admin.actions')}</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {listings.map((listing, index) => {
            console.log(`Rendering listing ${index}:`, {
              id: listing.id,
              title: listing.title,
              make: listing.make,
              model: listing.model,
              price: listing.price
            });
            return (
            <TableRow
              key={listing.id}
              className="border-b hover:bg-muted/50"
              style={{
                minHeight: '60px',
                backgroundColor: index % 2 === 0 ? '#f9f9f9' : '#ffffff',
                border: '1px solid #e0e0e0'
              }}
            >
              <TableCell
                className="font-medium p-4"
                style={{
                  border: '1px solid #ccc',
                  backgroundColor: '#fff',
                  color: '#000',
                  minHeight: '40px'
                }}
              >
                <Link
                  to={`/listings/${listing.id}`}
                  className="hover:underline text-primary"
                  style={{ color: '#0066cc', textDecoration: 'underline' }}
                >
                  {listing.title || 'No Title'}
                </Link>
              </TableCell>
              <TableCell
                className="p-4"
                style={{ border: '1px solid #ccc', backgroundColor: '#fff', color: '#000' }}
              >
                {listing.make || 'No Make'}
              </TableCell>
              <TableCell
                className="p-4"
                style={{ border: '1px solid #ccc', backgroundColor: '#fff', color: '#000' }}
              >
                {listing.model || 'No Model'}
              </TableCell>
              <TableCell
                className="p-4"
                style={{ border: '1px solid #ccc', backgroundColor: '#fff', color: '#000' }}
              >
                ${listing.price ? listing.price.toLocaleString() : '0'}
              </TableCell>
              <TableCell>
                {listing.featured ? (
                  <Badge variant="default" className="bg-yellow-500">
                    <Star className="h-3 w-3 mr-1 fill-current" />
                    {t('listings.featured')}
                  </Badge>
                ) : (
                  <Badge variant="outline">
                    {t('admin.notFeatured')}
                  </Badge>
                )}
              </TableCell>
              <TableCell>
                {listing.vip_status ? (
                  <Badge variant="default" className="bg-blue-500">
                    <BadgeCheck className="h-3 w-3 mr-1 fill-current" />
                    {t('admin.vip')}
                  </Badge>
                ) : (
                  <Badge variant="outline">
                    {t('admin.notVip')}
                  </Badge>
                )}
              </TableCell>
              <TableCell>
                {listing.paid_status ? (
                  <Badge variant="default" className="bg-green-500">
                    <BadgeDollarSign className="h-3 w-3 mr-1 fill-current" />
                    {t('admin.paid')}
                  </Badge>
                ) : (
                  <Badge variant="outline">
                    {t('admin.notPaid')}
                  </Badge>
                )}
              </TableCell>
              <TableCell>{formatDate(listing.created_at)}</TableCell>
              <TableCell>
                <div className="flex space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => onFeatureToggle(listing.id, listing.featured)}
                    title={listing.featured ? t('admin.removeFromFeatured') : t('admin.addToFeatured')}
                  >
                    <Star className={`h-4 w-4 ${listing.featured ? 'text-yellow-500 fill-yellow-500' : ''}`} />
                  </Button>

                  {onVipStatusToggle && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => onVipStatusToggle(listing.id, listing.vip_status || false)}
                      title={listing.vip_status ? t('admin.removeVip') : t('admin.setVip')}
                    >
                      <BadgeCheck className={`h-4 w-4 ${listing.vip_status ? 'text-blue-500 fill-blue-500' : ''}`} />
                    </Button>
                  )}

                  {onPaidStatusToggle && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => onPaidStatusToggle(listing.id, listing.paid_status || false)}
                      title={listing.paid_status ? t('admin.markAsUnpaid') : t('admin.markAsPaid')}
                    >
                      <BadgeDollarSign className={`h-4 w-4 ${listing.paid_status ? 'text-green-500 fill-green-500' : ''}`} />
                    </Button>
                  )}

                  <Link to={`/edit-listing/${listing.id}`}>
                    <Button variant="outline" size="sm" title={t('admin.editListing')}>
                      <Edit className="h-4 w-4" />
                    </Button>
                  </Link>

                  {onBlockToggle && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => onBlockToggle(listing.id, false)}
                      title={t('admin.blockListing')}
                    >
                      <Ban className="h-4 w-4 text-orange-500" />
                    </Button>
                  )}

                  <AlertDialog>
                    <AlertDialogTrigger asChild>
                      <Button variant="outline" size="sm" title={t('admin.deleteListing')}>
                        <Trash2 className="h-4 w-4 text-destructive" />
                      </Button>
                    </AlertDialogTrigger>
                    <AlertDialogContent>
                      <AlertDialogHeader>
                        <AlertDialogTitle>{t('admin.confirmDelete')}</AlertDialogTitle>
                        <AlertDialogDescription>
                          {t('admin.deleteListingWarning')}
                        </AlertDialogDescription>
                      </AlertDialogHeader>
                      <AlertDialogFooter>
                        <AlertDialogCancel>{t('admin.no')}</AlertDialogCancel>
                        <AlertDialogAction
                          onClick={() => onDelete(listing.id)}
                          className="bg-destructive text-destructive-foreground"
                        >
                          {t('admin.yes')}
                        </AlertDialogAction>
                      </AlertDialogFooter>
                    </AlertDialogContent>
                  </AlertDialog>
                </div>
              </TableCell>
            </TableRow>
            );
          })}
        </TableBody>
      </Table>
    </div>
  );
};

export default ListingTable;
