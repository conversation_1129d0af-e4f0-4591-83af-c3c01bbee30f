
import React from 'react';
import { useLanguage } from '@/hooks/useLanguage';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Search, RefreshCcw, Star, CreditCard } from 'lucide-react';

interface UserSearchAndFilterProps {
  searchTerm: string;
  setSearchTerm: (value: string) => void;
  roleFilter: string;
  setRoleFilter: (value: string) => void;
  statusFilter: string;
  setStatusFilter: (value: string) => void;
  vipFilter: string;
  setVipFilter: (value: string) => void;
  paidFilter: string;
  setPaidFilter: (value: string) => void;
  refreshUsers: () => void;
  isLoading: boolean;
}

const UserSearchAndFilter: React.FC<UserSearchAndFilterProps> = ({
  searchTerm,
  setSearchTerm,
  roleFilter,
  setRoleFilter,
  statusFilter,
  setStatusFilter,
  vipFilter,
  setVipFilter,
  paidFilter,
  setPaidFilter,
  refreshUsers,
  isLoading,
}) => {
  const { t } = useLanguage();
  
  return (
    <div className="space-y-4">
      <div className="flex flex-col md:flex-row gap-4 justify-between">
        <div className="relative flex-grow max-w-md">
          <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder={t('admin.search')}
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
        
        <div className="flex gap-2">
          <Button onClick={refreshUsers} disabled={isLoading}>
            <RefreshCcw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
            {isLoading ? t('loading') : t('admin.refresh')}
          </Button>
        </div>
      </div>
      
      <div className="flex flex-wrap gap-2">
        <div className="flex items-center gap-2">
          <Select value={roleFilter} onValueChange={setRoleFilter}>
            <SelectTrigger className="w-[140px]">
              <SelectValue placeholder={t('admin.role')} />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">{t('all')}</SelectItem>
              <SelectItem value="admin">{t('admin.admin')}</SelectItem>
              <SelectItem value="moderator">{t('admin.moderator')}</SelectItem>
              <SelectItem value="user">{t('admin.regular')}</SelectItem>
            </SelectContent>
          </Select>
        </div>
        
        <div className="flex items-center gap-2">
          <Select value={statusFilter} onValueChange={setStatusFilter}>
            <SelectTrigger className="w-[140px]">
              <SelectValue placeholder={t('admin.status')} />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">{t('all')}</SelectItem>
              <SelectItem value="active">{t('admin.active')}</SelectItem>
              <SelectItem value="inactive">{t('admin.inactive')}</SelectItem>
            </SelectContent>
          </Select>
        </div>
        
        <div className="flex items-center gap-2">
          <Select value={vipFilter} onValueChange={setVipFilter}>
            <SelectTrigger className="w-[140px]">
              <div className="flex items-center">
                <Star className="h-4 w-4 mr-2 text-amber-500" />
                <span>{t('admin.vipStatus')}</span>
              </div>
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">{t('all')}</SelectItem>
              <SelectItem value="vip">{t('admin.vip')}</SelectItem>
              <SelectItem value="non-vip">{t('admin.notVip')}</SelectItem>
            </SelectContent>
          </Select>
        </div>
        
        <div className="flex items-center gap-2">
          <Select value={paidFilter} onValueChange={setPaidFilter}>
            <SelectTrigger className="w-[140px]">
              <div className="flex items-center">
                <CreditCard className="h-4 w-4 mr-2 text-emerald-500" />
                <span>{t('admin.paidStatus')}</span>
              </div>
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">{t('all')}</SelectItem>
              <SelectItem value="paid">{t('admin.paid')}</SelectItem>
              <SelectItem value="non-paid">{t('admin.notPaid')}</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>
    </div>
  );
};

export default UserSearchAndFilter;
