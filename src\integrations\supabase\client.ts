// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://ntktrwxtvatugytnkmbj.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im50a3Ryd3h0dmF0dWd5dG5rbWJqIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDIzMjA3OTUsImV4cCI6MjA1Nzg5Njc5NX0.zHDr3onDlSBDytcMcO8Lb_xqtoD-9oCk4eTYL1AkXgY";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);