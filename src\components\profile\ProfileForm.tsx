
import React from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useLanguage } from '@/hooks/useLanguage';

interface ProfileFormProps {
  username: string;
  fullName: string;
  phone: string;
  userEmail?: string;
  isSaving: boolean;
  onUsernameChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onFullNameChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onPhoneChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onSave: () => void;
}

const ProfileForm: React.FC<ProfileFormProps> = ({
  username,
  fullName,
  phone,
  userEmail,
  isSaving,
  onUsernameChange,
  onFullNameChange,
  onPhoneChange,
  onSave
}) => {
  const { t } = useLanguage();

  return (
    <Card className="md:col-span-2">
      <CardHeader>
        <CardTitle>{t('profile.personalInfo')}</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="email">{t('ui.email')}</Label>
              <Input
                id="email"
                type="email"
                value={userEmail || ''}
                disabled
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="username">{t('profile.username')}</Label>
              <Input
                id="username"
                value={username}
                onChange={onUsernameChange}
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="fullName">{t('profile.fullName')}</Label>
              <Input
                id="fullName"
                value={fullName}
                onChange={onFullNameChange}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="phone">{t('profile.phone')}</Label>
              <Input
                id="phone"
                type="tel"
                value={phone}
                onChange={onPhoneChange}
              />
            </div>
          </div>

          <div className="pt-4 flex justify-end">
            <Button onClick={onSave} disabled={isSaving}>
              {isSaving ? t('ui.saving') : t('profile.saveChanges')}
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default ProfileForm;
