export const listings = {
  "myListings": "Mis anuncios",
  "favorites": "Favoritos",
  "noListingsYet": "Aún no hay anuncios. ¡Crea uno ahora!",
  "noFavoritesYet": "Aún no hay favoritos. ¡Explora los anuncios!",
  "exploreListings": "Explorar anuncios",
  "title": "Título",
  "make": "Marca",
  "model": "Modelo",
  "year": "Año",
  "price": "Precio",
  "description": "Descripción",
  "noDescriptionProvided": "No se proporcionó descripción",
  "images": "Imágenes",
  "updateListing": "Actualizar anuncio",
  "deleteListing": "Eliminar anuncio",
  "confirmDelete": "Confirmar eliminación",
  "deleteListingWarning": "¿Estás seguro de que quieres eliminar este anuncio? Esta acción no se puede deshacer.",
  "showing": "Mostrando",
  "of": "de",
  "results": "resultados",
  "hideFilters": "Ocultar filtros",
  "easySearch": "Búsqueda fácil",
  "qualityParts": "Piezas de calidad",
  "bestPrices": "Mejores precios",
  "secureTransactions": "Transacciones seguras",
  "whyChoose": "¿Por qué elegirnos?",
  "readyToSell": "¿Listo para vender tus piezas?",
  "carParts": "Partes de auto",
  "engineTransmission": "Motor y transmisión",
  "viewDetails": "Ver detalles",
  "selectLocation": "Seleccionar ubicación",
  "enterLocationToSearch": "Ingresa una ubicación para buscar piezas cerca de ti.",
  "enterCity": "Ingresar ciudad",
  "listingsFound": "anuncios encontrados",
  "filterBy": "Filtrar por",
  "featured": "Destacado",
  "notFeatured": "No destacado",
  "topListings": "Repuestos de alta calidad",
  "featuredListings": "Anuncios Destacados",
  "listingsRefreshed": "Anuncios actualizados",
  "refreshError": "Error al actualizar",
  "noListingsFound": "No se encontraron anuncios",
  "createFirstListing": "Crear primer anuncio",
  "quickView": "Vista rápida",
  "save": "Guardar",
  "saved": "Guardado",
  "sortBy": "Ordenar por",
  "newest": "Más recientes",
  "oldest": "Más antiguos",
  "priceLowToHigh": "Precio: de menor a mayor",
  "priceHighToLow": "Precio: de mayor a menor",
  "location": "Ubicación",
  "viewDetailsBtn": "Ver detalles",
  "deleteListingConfirmation": "¿Estás seguro de que quieres eliminar este anuncio? Esta acción no se puede deshacer.",
  "cancel": "Cancelar",
  "delete": "Eliminar",
  "specifications": "Especificaciones",
  "contactSeller": "Contactar vendedor",
  "signInToContactSeller": "Inicia sesión para contactar al vendedor",
  "sellerInformation": "Información del vendedor",
  "seller": "Vendedor",
  "memberSince": "Miembro desde",
  "responseRate": "Tasa de respuesta",
  "averageResponseTime": "Tiempo promedio de respuesta",
  "hours": "horas",
  "mapUnavailable": "Mapa no disponible",
  "failedToLoadListing": "Error al cargar el anuncio",
  "listingDeletedSuccessfully": "Anuncio eliminado con éxito",
  "failedToDeleteListing": "Error al eliminar el anuncio",
  "messageSent": "Mensaje enviado con éxito",
  "errorSendingMessage": "Error al enviar el mensaje",
  "sendMessage": "Enviar mensaje",
  "call": "Llamar",
  "sending": "Enviando...",
  "deleting": "Eliminando...",
  "typeYourMessage": "Escribe tu mensaje aquí...",
  "send": "Enviar",
  "addToFavorites": "Añadir a favoritos",
  "listingNotFound": "Anuncio no encontrado",
  "listingMayHaveBeenRemoved": "Es posible que este anuncio haya sido eliminado",
  "backToListings": "Volver a los anuncios",
  "removedFromFavorites": "Eliminado de favoritos",
  "addedToFavorites": "Añadido a favoritos",
  "createListing": "Crear anuncio",
  "selectMake": "Seleccionar marca",
  "selectModel": "Seleccionar modelo",
  "from": "Desde",
  "to": "Hasta",
  "yearFrom": "Año desde",
  "yearTo": "Año hasta",
  "min": "Mín",
  "max": "Máx",
  "any": "Cualquiera",
  "all": "Todos",
  "applyFilters": "Aplicar filtros",
  "reset": "Reiniciar",
  "filter": "Filtro",
  "filtersApplied": "Filtros aplicados",
  "page": "Página",
  "canCreateOrAddTest": "Puedes crear uno nuevo o agregar ejemplos de prueba.",
  "addTestListings": "Agregar ejemplos",
  "testListingsCreated": "Ejemplos de anuncios creados",
  "testListingsError": "Error al crear ejemplos",
  "refreshingListings": "Actualizando anuncios...",
  "listingsFetchError": "Error al cargar anuncios",
  "singleColumnView": "Vista de una columna",
  "multiColumnView": "Vista de varias columnas",
  "bodyType": "Tipo de carrocería",
  "selectBodyType": "Seleccionar tipo de carrocería",
  "fuelType": "Tipo de combustible",
  "selectFuelType": "Seleccionar tipo de combustible",
  "transmission": "Transmisión",
  "selectTransmission": "Seleccionar transmisión",
  "color": "Color",
  "selectColor": "Seleccionar color",
  "mileage": "Kilometraje",
  "enterMileage": "Ingresar kilometraje",
  "additionalDetails": "Detalles adicionales",
  "vehicleDetails": "Detalles del vehículo",
  "requiredField": "Campo obligatorio",
  "uploadImages": "Subir imágenes",
  "maxImagesAllowed": "Máximo {count} imágenes permitidas",
  "vehicleSpecifications": "Especificaciones del vehículo",
  "sellerContact": "Información de contacto del vendedor",
  "contactSellerByPhone": "Puedes llamar al vendedor directamente usando este número",
  "callNow": "Llamar ahora",
  "signInToCall": "Inicia sesión para llamar al vendedor",
  "signInWithGoogle": "Iniciar sesión con Google",
  "writeToSeller": "Escribir al vendedor",
  "enterYourMessage": "Escribe tu mensaje aquí...",
  "messageSentSuccessfully": "Mensaje enviado con éxito",
  "failedToDetermineSenderOrRecipient": "No se pudo determinar el remitente o destinario",
  "sellerPhone": "Teléfono del vendedor",
  "youCanCallSeller": "Puedes llamar al vendedor con este número",
  "pageNotFound": "Página no encontrada",
  "pageNotFoundDescription": "La página que buscas no existe o ha sido movida.",
  "backToHome": "Volver a inicio",
  "listingsLabel": "anuncios",
  "searchAndFilters": "Búsqueda y filtros",
  "mustBeLoggedInToSave": "Debes iniciar sesión para guardar anuncios",
  "failedToUpdateSaveStatus": "Error al actualizar el estado de guardado"
};
