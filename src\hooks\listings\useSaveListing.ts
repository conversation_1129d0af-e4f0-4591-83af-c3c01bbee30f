
import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';
import { toast } from 'sonner';

export const useSaveListing = (listingId: string) => {
  const { isAuthenticated, user } = useAuth();
  const [isSaved, setIsSaved] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  
  useEffect(() => {
    const checkIfSaved = async () => {
      if (!isAuthenticated || !user) {
        setIsSaved(false);
        return;
      }
      
      try {
        setIsLoading(true);
        const { data, error } = await supabase
          .from('favorites')
          .select('id')
          .eq('user_id', user.id)
          .eq('listing_id', listingId)
          .single();
          
        if (error && error.code !== 'PGRST116') {
          console.error('Error checking favorites:', error);
          return;
        }
        
        setIsSaved(!!data);
      } catch (error) {
        console.error('Error checking favorites:', error);
      } finally {
        setIsLoading(false);
      }
    };
    
    checkIfSaved();
  }, [listingId, isAuthenticated, user]);
  
  const toggleSave = async () => {
    if (!isAuthenticated || !user) {
      toast.error("Authentication Required", {
        description: "You must be logged in to save listings"
      });
      return;
    }
    
    try {
      setIsLoading(true);
      
      if (isSaved) {
        const { error } = await supabase
          .from('favorites')
          .delete()
          .eq('user_id', user.id)
          .eq('listing_id', listingId);
          
        if (error) throw error;
        
        setIsSaved(false);
        toast.success("Removed from Saved", {
          description: "Listing removed from your saved items"
        });
      } else {
        const { error } = await supabase
          .from('favorites')
          .insert({
            user_id: user.id,
            listing_id: listingId
          });
          
        if (error) throw error;
        
        setIsSaved(true);
        toast.success("Saved", {
          description: "Listing added to your saved items"
        });
      }
    } catch (error) {
      console.error('Error toggling favorite:', error);
      toast.error("Error", {
        description: "Failed to update saved status"
      });
      throw error;
    } finally {
      setIsLoading(false);
    }
  };
  
  return { isSaved, isLoading, toggleSave };
};
