
import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { RefreshCcw } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useLanguage } from '@/hooks/useLanguage';
import PendingListingItem from './PendingListingItem';
import PendingListingsPagination from './PendingListingsPagination';
import PendingListingsToggle from './PendingListingsToggle';
import { paginateListings } from './utils';
import { Listing } from '@/hooks/listings/types';

interface PendingListingsProps {
  pendingListings: Array<{
    id: string;
    title: string;
    created_at: string;
    onApprove: () => Promise<void>;
  }>;
  isLoading: boolean;
  isRefreshing: boolean;
  autoApprove: boolean;
  onAutoApproveChange: (checked: boolean) => Promise<void>;
  onRefresh: () => Promise<void>;
}

const PendingListings: React.FC<PendingListingsProps> = ({
  pendingListings,
  isLoading,
  isRefreshing,
  autoApprove,
  onAutoApproveChange,
  onRefresh
}) => {
  const { t } = useLanguage();
  const [currentPage, setCurrentPage] = useState(1);
  const [isAutoApproveToggling, setIsAutoApproveToggling] = useState(false);
  
  const itemsPerPage = 5;
  
  // Calculate pagination
  const { currentItems, totalPages } = paginateListings(pendingListings, currentPage, itemsPerPage);
  
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };
  
  const handleAutoApproveToggle = async () => {
    try {
      setIsAutoApproveToggling(true);
      await onAutoApproveChange(!autoApprove);
    } catch (error) {
      console.error('Error toggling auto-approve:', error);
    } finally {
      setIsAutoApproveToggling(false);
    }
  };
  
  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-lg font-medium">
          {t('admin.pendingListings')}
        </CardTitle>
        <PendingListingsToggle
          autoApprove={autoApprove}
          isAutoApproveToggling={isAutoApproveToggling}
          onAutoApproveToggle={handleAutoApproveToggle}
        />
      </CardHeader>
      
      <CardContent>
        <p className="text-sm text-muted-foreground mb-4">
          {t('admin.pendingApprovalDescription')}
        </p>
        
        {isLoading || isRefreshing ? (
          <div className="flex justify-center py-4">
            <RefreshCcw className="h-6 w-6 animate-spin text-primary" />
          </div>
        ) : pendingListings.length > 0 ? (
          <div className="mt-4 space-y-2">
            {currentItems.map((listing) => (
              <PendingListingItem
                key={listing.id}
                id={listing.id}
                title={listing.title}
                created_at={listing.created_at}
                onApprove={listing.onApprove}
              />
            ))}
          </div>
        ) : (
          <p className="text-center py-4">
            {t('admin.noListingsPending')}
          </p>
        )}
        
        {pendingListings.length > itemsPerPage && (
          <PendingListingsPagination
            currentPage={currentPage}
            totalPages={totalPages}
            onPageChange={handlePageChange}
          />
        )}
        
        <div className="flex justify-between mt-4">
          <Button
            variant="outline"
            size="sm"
            onClick={onRefresh}
            disabled={isRefreshing}
          >
            <RefreshCcw className={`h-4 w-4 mr-2 ${isRefreshing ? 'animate-spin' : ''}`} />
            {t('admin.refresh')}
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

export default PendingListings;
