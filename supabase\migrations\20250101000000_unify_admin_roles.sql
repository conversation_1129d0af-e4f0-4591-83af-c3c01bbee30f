-- Migration to unify admin role system
-- This migration ensures consistency between is_admin and role fields

-- First, add role column if it doesn't exist
DO $$ 
BEGIN
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                 WHERE table_name = 'profiles' 
                 AND column_name = 'role' 
                 AND table_schema = 'public') THEN
    ALTER TABLE public.profiles ADD COLUMN role TEXT DEFAULT 'user';
  END IF;
END $$;

-- Update role based on is_admin field for consistency
UPDATE public.profiles 
SET role = CASE 
  WHEN is_admin = true THEN 'admin'
  ELSE 'user'
END
WHERE role IS NULL OR role = '';

-- Create function to check if user is admin (supports both fields)
CREATE OR REPLACE FUNCTION public.is_user_admin(user_id UUID)
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM public.profiles
    WHERE id = user_id
    AND (role = 'admin' OR is_admin = true)
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to ensure admin consistency
CREATE OR REPLACE FUNCTION public.sync_admin_fields()
RETURNS TRIGGER AS $$
BEGIN
  -- If role is set to admin, ensure is_admin is true
  IF NEW.role = 'admin' THEN
    NEW.is_admin = true;
  END IF;
  
  -- If is_admin is set to true, ensure role is admin
  IF NEW.is_admin = true THEN
    NEW.role = 'admin';
  END IF;
  
  -- If role is not admin and is_admin is not explicitly true, set both to user/false
  IF NEW.role != 'admin' AND NEW.is_admin IS NOT true THEN
    NEW.role = 'user';
    NEW.is_admin = false;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to maintain consistency
DROP TRIGGER IF EXISTS sync_admin_fields_trigger ON public.profiles;
CREATE TRIGGER sync_admin_fields_trigger
  BEFORE INSERT OR UPDATE ON public.profiles
  FOR EACH ROW
  EXECUTE FUNCTION public.sync_admin_fields();

-- Update existing RLS policies to use the new function
-- For listings table (if admin-only operations exist)
DO $$
BEGIN
  -- Drop existing admin policies if they exist
  DROP POLICY IF EXISTS "Admins can manage all listings" ON public.listings;
  
  -- Create new admin policy for listings
  CREATE POLICY "Admins can manage all listings" ON public.listings
    FOR ALL
    TO authenticated
    USING (public.is_user_admin(auth.uid()))
    WITH CHECK (public.is_user_admin(auth.uid()));
EXCEPTION
  WHEN undefined_table THEN
    -- Table doesn't exist yet, skip
    NULL;
END $$;

-- Update admin_notifications policies
DROP POLICY IF EXISTS "Admins can view all notifications" ON public.admin_notifications;
DROP POLICY IF EXISTS "Admins can update notifications" ON public.admin_notifications;

CREATE POLICY "Admins can view all notifications" ON public.admin_notifications
  FOR SELECT
  TO authenticated
  USING (public.is_user_admin(auth.uid()));

CREATE POLICY "Admins can update notifications" ON public.admin_notifications
  FOR UPDATE
  TO authenticated
  USING (public.is_user_admin(auth.uid()));

-- Update listing_reports policies
DROP POLICY IF EXISTS "Admins can view all reports" ON public.listing_reports;
DROP POLICY IF EXISTS "Admins can update reports" ON public.listing_reports;

CREATE POLICY "Admins can view all reports" ON public.listing_reports
  FOR SELECT
  TO authenticated
  USING (auth.uid() = user_id OR public.is_user_admin(auth.uid()));

CREATE POLICY "Admins can update reports" ON public.listing_reports
  FOR UPDATE
  TO authenticated
  USING (public.is_user_admin(auth.uid()));

-- Grant execute permission on the function to authenticated users
GRANT EXECUTE ON FUNCTION public.is_user_admin(UUID) TO authenticated;
