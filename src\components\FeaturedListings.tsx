
import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { useLanguage } from '@/hooks/useLanguage';
import { useTranslation } from 'react-i18next';
import { Button } from '@/components/ui/button';
import { RefreshCcw, ChevronRight } from 'lucide-react';
import ListingCard from '@/components/ListingCard';
import { useListings } from '@/hooks/useListings';
import { toast } from 'sonner';
import i18next from '@/utils/i18n';

const FeaturedListings: React.FC = () => {
  const { language } = useLanguage();
  const { t } = useTranslation();
  const { listings, isLoading, fetchListings } = useListings();
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Get latest listings (most recent first)
  const latestListings = [...listings]
    .sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())
    .slice(0, 4);

  const handleRefresh = async () => {
    try {
      setIsRefreshing(true);
      await fetchListings();
      toast.success(t('listings.listingsRefreshed'));
    } catch (error) {
      console.error("Error refreshing listings:", error);
      toast.error(t('listings.refreshError'));
    } finally {
      setIsRefreshing(false);
    }
  };

  // These are the translations we need to ensure are working
  const latestUpdatesText = t('listings.latestUpdates');
  const recentlyAddedText = t('listings.recentlyAddedListings');
  const refreshText = t('listings.refresh');
  const viewAllText = t('listings.viewAll');

  return (
    <section className="py-12 bg-secondary/20">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-8">
          <div>
            <h2 className="text-3xl font-bold">
              {latestUpdatesText}
            </h2>
            <p className="text-muted-foreground mt-1">
              {recentlyAddedText}
            </p>
          </div>

          <div className="flex items-center gap-3">
            <Button
              variant="outline"
              size="sm"
              onClick={handleRefresh}
              disabled={isRefreshing}
              className="rounded-full"
            >
              <RefreshCcw className="mr-2 h-4 w-4" />
              {refreshText}
            </Button>

            <Button
              variant="outline"
              size="sm"
              asChild
              className="rounded-full"
            >
              <Link to="/listings">
                {viewAllText}
                <ChevronRight className="ml-1 h-4 w-4" />
              </Link>
            </Button>
          </div>
        </div>

        {isLoading ? (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
            {[1, 2, 3, 4].map(i => (
              <div key={i} className="bg-card rounded-xl overflow-hidden shadow-subtle animate-pulse">
                <div className="aspect-[3/2] bg-muted"></div>
                <div className="p-4 space-y-3">
                  <div className="h-5 bg-muted rounded-md w-4/5"></div>
                  <div className="h-4 bg-muted rounded-md w-1/2"></div>
                  <div className="h-4 bg-muted rounded-md w-2/3"></div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <>
            {latestListings.length === 0 ? (
              <div className="text-center py-8">
                <p className="text-muted-foreground mb-4">{t('noListingsFound')}</p>
                <Button asChild>
                  <Link to="/create-listing">{t('createFirstListing')}</Link>
                </Button>
              </div>
            ) : (
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
                {latestListings.map(listing => (
                  <ListingCard
                    key={listing.id}
                    id={listing.id}
                    title={listing.title}
                    year={listing.year}
                    make={listing.make}
                    model={listing.model}
                    price={Number(listing.price)}
                    location={listing.location}
                    imageUrl={listing.image_urls && listing.image_urls.length > 0
                      ? listing.image_urls[0]
                      : 'https://images.unsplash.com/photo-1533473359331-0135ef1b58bf?auto=format&fit=crop&q=80&w=2670&ixlib=rb-4.0.3'}
                    featured={listing.featured}
                    createdAt={listing.created_at}
                  />
                ))}
              </div>
            )}
          </>
        )}
      </div>
    </section>
  );
};

export default FeaturedListings;
