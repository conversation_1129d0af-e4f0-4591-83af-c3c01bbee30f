
import React from 'react';
import { useLanguage } from '@/hooks/useLanguage';
import { FormField, FormItem, FormLabel, FormControl, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { carBodyTypes, carFuelTypes, carTransmissionTypes, carColors } from '@/utils/dictionaries/vehicles';
import { useFormContext } from './ListingFormContext';

const AdditionalDetailsForm: React.FC = () => {
  const { t, language } = useLanguage();
  const { form } = useFormContext();

  // Get the appropriate language array for each dictionary
  const getLocalizedArray = (dictionary: { ru: string[]; en: string[] }) => {
    return dictionary[language as keyof typeof dictionary] || dictionary.en;
  };

  return (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold">{t('listings.vehicleSpecifications')}</h3>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <FormField
          control={form.control}
          name="bodyType"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{t('listings.bodyType')}</FormLabel>
              <Select value={field.value} onValueChange={field.onChange}>
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder={t('listings.selectBodyType')} />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {getLocalizedArray(carBodyTypes).map((type) => (
                    <SelectItem key={type} value={type}>
                      {type}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="fuelType"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{t('listings.fuelType')}</FormLabel>
              <Select value={field.value} onValueChange={field.onChange}>
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder={t('listings.selectFuelType')} />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {getLocalizedArray(carFuelTypes).map((type) => (
                    <SelectItem key={type} value={type}>
                      {type}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="transmission"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{t('listings.transmission')}</FormLabel>
              <Select value={field.value} onValueChange={field.onChange}>
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder={t('listings.selectTransmission')} />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {getLocalizedArray(carTransmissionTypes).map((type) => (
                    <SelectItem key={type} value={type}>
                      {type}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="color"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{t('listings.color')}</FormLabel>
              <Select value={field.value} onValueChange={field.onChange}>
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder={t('listings.selectColor')} />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {getLocalizedArray(carColors).map((colorOption) => (
                    <SelectItem key={colorOption} value={colorOption}>
                      {colorOption}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="mileage"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{t('listings.mileage')}</FormLabel>
              <FormControl>
                <Input
                  placeholder={t('listings.enterMileage')}
                  type="number"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>
    </div>
  );
};

export default AdditionalDetailsForm;
