
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { ListingFormData, listingFormSchema } from './types';
import { useListingFormValidation } from './useListingFormValidation';

export const useListingFormState = (existingData?: any) => {
  const { schema } = useListingFormValidation();
  
  const defaultValues: ListingFormData = {
    title: existingData?.title || '',
    description: existingData?.description || '',
    price: existingData?.price || '',
    year: existingData?.year || new Date().getFullYear(),
    make: existingData?.make || '',
    model: existingData?.model || '',
    location: existingData?.location || '',
    featured: existingData?.featured || false,
    images: existingData?.image_urls || [],
    bodyType: existingData?.body_type || '',
    fuelType: existingData?.fuel_type || '',
    transmission: existingData?.transmission || '',
    color: existingData?.color || '',
    mileage: existingData?.mileage?.toString() || ''
  };

  const form = useForm<ListingFormData>({
    resolver: zodResolver(schema),
    defaultValues,
    mode: 'onChange'
  });

  const [customMake, setCustomMake] = useState('');
  const [customModel, setCustomModel] = useState('');
  const [customLocation, setCustomLocation] = useState('');

  return {
    form,
    customValues: {
      customMake,
      setCustomMake,
      customModel,
      setCustomModel,
      customLocation,
      setCustomLocation
    }
  };
};
