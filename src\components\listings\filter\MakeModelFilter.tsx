
import React, { useState, useEffect } from 'react';
import { useLanguage } from '@/hooks/useLanguage';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { carMakes, carModelsByMake, defaultModels } from '@/utils/dictionaries/vehicles';

interface MakeModelFilterProps {
  make: string;
  setMake: (value: string) => void;
  model: string;
  setModel: (value: string) => void;
}

const MakeModelFilter: React.FC<MakeModelFilterProps> = ({
  make,
  setMake,
  model,
  setModel
}) => {
  const { t } = useLanguage();
  const [modelOptions, setModelOptions] = useState<string[]>([]);

  // Update model options when make changes
  useEffect(() => {
    if (make && make !== 'all') {
      setModelOptions(carModelsByMake[make] || defaultModels);
    } else {
      setModelOptions([]);
      setModel(''); // Reset model when make changes
    }
  }, [make, setModel]);

  return (
    <>
      <div className="space-y-3">
        <h3 className="text-sm font-medium">{t('listings.make')}</h3>
        <Select value={make} onValueChange={setMake}>
          <SelectTrigger className="w-full">
            <SelectValue placeholder={t('listings.selectMake')} />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">{t('listings.all')}</SelectItem>
            {carMakes.map((carMake) => (
              <SelectItem key={carMake} value={carMake}>
                {carMake}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {make && make !== 'all' && (
        <div className="space-y-3">
          <h3 className="text-sm font-medium">{t('listings.model')}</h3>
          <Select value={model} onValueChange={setModel}>
            <SelectTrigger className="w-full">
              <SelectValue placeholder={t('listings.selectModel')} />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all_models">{t('listings.all')}</SelectItem>
              {modelOptions.map((carModel) => (
                <SelectItem key={carModel} value={carModel}>
                  {carModel}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      )}
    </>
  );
};

export default MakeModelFilter;
