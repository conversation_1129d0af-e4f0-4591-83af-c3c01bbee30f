
import React, { memo } from 'react';
import { <PERSON> } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Heart, Eye } from 'lucide-react';
import { cn } from '@/lib/utils';
import { useLanguage } from '@/hooks/useLanguage';
import { toast } from 'sonner';

interface CardImageProps {
  id: string;
  imageUrl: string;
  title: string;
  featured: boolean;
  isSaved: boolean;
  isLoading: boolean;
  isAuthenticated: boolean;
  onToggleSave: () => Promise<void>;
}

const CardImage: React.FC<CardImageProps> = ({
  id,
  imageUrl,
  title,
  featured,
  isSaved,
  isLoading,
  isAuthenticated,
  onToggleSave
}) => {
  const { t } = useLanguage();
  const listingUrl = `/listings/${id}`;

  const handleToggleSave = async (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    if (!isAuthenticated) {
      toast.error(t('auth.authenticationRequired'), {
        description: t('listings.mustBeLoggedInToSave')
      });
      return;
    }

    try {
      await onToggleSave();
    } catch (error) {
      console.error('Error toggling save status:', error);
      toast.error(t('error'), {
        description: t('listings.failedToUpdateSaveStatus')
      });
    }
  };

  return (
    <div className="relative overflow-hidden aspect-[3/2]">
      {featured && (
        <div className="absolute top-4 left-0 z-10 bg-primary text-white text-xs font-medium py-1 pl-2 pr-3 rounded-r-full">
          {t('listings.featured')}
        </div>
      )}

      <Link to={listingUrl}>
        <img
          src={imageUrl}
          alt={title}
          loading="lazy"
          className="w-full h-full object-cover transform transition-transform duration-700 group-hover:scale-105"
        />
        <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

        <div className="absolute bottom-4 left-0 right-0 px-4 flex justify-between opacity-0 group-hover:opacity-100 transition-opacity duration-300">
          <Button size="sm" variant="secondary" className="rounded-full" onClick={handleToggleSave}>
            <Heart className={cn("h-4 w-4 mr-1", isSaved && "fill-current text-red-500")} />
            {isSaved ? t('listings.saved') : t('listings.save')}
          </Button>
          <Button size="sm" variant="secondary" className="rounded-full" asChild>
            <Link to={listingUrl}>
              <Eye className="h-4 w-4 mr-1" />
              {t('listings.quickView')}
            </Link>
          </Button>
        </div>
      </Link>

      <Button
        size="icon"
        variant="ghost"
        className={cn(
          "absolute top-4 right-4 h-8 w-8 bg-white/80 dark:bg-black/50 backdrop-blur-sm rounded-full",
          isSaved ? "text-red-500" : "text-foreground hover:text-primary"
        )}
        onClick={handleToggleSave}
        disabled={isLoading}
      >
        <Heart className={cn("h-4 w-4", isSaved && "fill-current")} />
      </Button>
    </div>
  );
};

// Memoize the component to prevent unnecessary re-renders
export default memo(CardImage);
