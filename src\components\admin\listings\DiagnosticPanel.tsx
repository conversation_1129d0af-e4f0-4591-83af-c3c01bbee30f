import React, { useState } from 'react';
import { createTestData, checkDatabaseConnection } from '@/utils/createTestData';

interface DiagnosticPanelProps {
  listings: any[];
  isLoading: boolean;
  totalItems: number;
  currentPage: number;
  itemsPerPage: number;
}

const DiagnosticPanel: React.FC<DiagnosticPanelProps> = ({
  listings,
  isLoading,
  totalItems,
  currentPage,
  itemsPerPage
}) => {
  const [testResult, setTestResult] = useState<string>('');
  const [isCreating, setIsCreating] = useState(false);
  const [isChecking, setIsChecking] = useState(false);

  const handleCreateTestData = async () => {
    setIsCreating(true);
    setTestResult('Creating test data...');

    try {
      const result = await createTestData();
      if (result.success) {
        setTestResult(`✅ Success! Created ${result.inserted} listings. Total: ${result.total}`);
      } else {
        setTestResult(`❌ Error: ${result.error}`);
      }
    } catch (error) {
      setTestResult(`❌ Error: ${error.message}`);
    } finally {
      setIsCreating(false);
    }
  };

  const handleCheckDatabase = async () => {
    setIsChecking(true);
    setTestResult('Checking database...');

    try {
      const result = await checkDatabaseConnection();
      if (result.success) {
        setTestResult(`✅ Database OK! Found ${result.count} listings`);
      } else {
        setTestResult(`❌ Database Error: ${result.error}`);
      }
    } catch (error) {
      setTestResult(`❌ Error: ${error.message}`);
    } finally {
      setIsChecking(false);
    }
  };

  return (
    <div style={{
      border: '3px solid #ff0000',
      backgroundColor: '#ffe6e6',
      padding: '20px',
      margin: '20px 0',
      borderRadius: '8px',
      fontFamily: 'monospace'
    }}>
      <h2 style={{ color: '#cc0000', marginBottom: '15px' }}>
        🔍 DIAGNOSTIC PANEL
      </h2>

      <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '20px' }}>
        <div>
          <h3 style={{ color: '#cc0000' }}>State Information:</h3>
          <ul style={{ listStyle: 'none', padding: 0 }}>
            <li><strong>isLoading:</strong> {isLoading.toString()}</li>
            <li><strong>totalItems:</strong> {totalItems}</li>
            <li><strong>currentPage:</strong> {currentPage}</li>
            <li><strong>itemsPerPage:</strong> {itemsPerPage}</li>
            <li><strong>listings.length:</strong> {listings.length}</li>
            <li><strong>Array.isArray(listings):</strong> {Array.isArray(listings).toString()}</li>
            <li><strong>typeof listings:</strong> {typeof listings}</li>
          </ul>
        </div>

        <div>
          <h3 style={{ color: '#cc0000' }}>Render Conditions:</h3>
          <ul style={{ listStyle: 'none', padding: 0 }}>
            <li><strong>!isLoading:</strong> {(!isLoading).toString()}</li>
            <li><strong>listings.length > 0:</strong> {(listings.length > 0).toString()}</li>
            <li><strong>Should render table:</strong> {(!isLoading && listings.length > 0).toString()}</li>
            <li><strong>Should show empty:</strong> {(!isLoading && listings.length === 0).toString()}</li>
          </ul>
        </div>
      </div>

      <div style={{ marginTop: '20px' }}>
        <h3 style={{ color: '#cc0000' }}>First 3 Listings Data:</h3>
        <pre style={{
          backgroundColor: '#f0f0f0',
          padding: '10px',
          borderRadius: '4px',
          overflow: 'auto',
          maxHeight: '200px',
          fontSize: '12px'
        }}>
          {JSON.stringify(listings.slice(0, 3), null, 2)}
        </pre>
      </div>

      <div style={{ marginTop: '20px' }}>
        <h3 style={{ color: '#cc0000' }}>Raw Listings Object:</h3>
        <pre style={{
          backgroundColor: '#f0f0f0',
          padding: '10px',
          borderRadius: '4px',
          overflow: 'auto',
          maxHeight: '100px',
          fontSize: '10px'
        }}>
          {typeof listings === 'object' ? JSON.stringify(listings, null, 2) : String(listings)}
        </pre>
      </div>

      <div style={{ marginTop: '20px' }}>
        <h3 style={{ color: '#cc0000' }}>Test Render:</h3>
        <div style={{ border: '1px solid #ccc', padding: '10px' }}>
          {listings.length === 0 ? (
            <p style={{ color: 'red', fontWeight: 'bold' }}>❌ NO LISTINGS TO RENDER</p>
          ) : (
            <div>
              <p style={{ color: 'green', fontWeight: 'bold' }}>✅ {listings.length} LISTINGS AVAILABLE</p>
              {listings.slice(0, 2).map((listing, index) => (
                <div key={listing?.id || index} style={{
                  border: '1px solid #999',
                  padding: '5px',
                  margin: '5px 0',
                  backgroundColor: '#fff'
                }}>
                  <strong>Listing {index + 1}:</strong> {listing?.title || 'NO TITLE'}
                  ({listing?.make || 'NO MAKE'} {listing?.model || 'NO MODEL'})
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      <div style={{ marginTop: '20px' }}>
        <h3 style={{ color: '#cc0000' }}>Database Actions:</h3>
        <div style={{ display: 'flex', gap: '10px', marginBottom: '10px' }}>
          <button
            onClick={handleCheckDatabase}
            disabled={isChecking}
            style={{
              padding: '10px 20px',
              backgroundColor: '#007bff',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: isChecking ? 'not-allowed' : 'pointer',
              opacity: isChecking ? 0.6 : 1
            }}
          >
            {isChecking ? 'Checking...' : 'Check Database'}
          </button>

          <button
            onClick={handleCreateTestData}
            disabled={isCreating}
            style={{
              padding: '10px 20px',
              backgroundColor: '#28a745',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: isCreating ? 'not-allowed' : 'pointer',
              opacity: isCreating ? 0.6 : 1
            }}
          >
            {isCreating ? 'Creating...' : 'Create Test Data'}
          </button>
        </div>

        {testResult && (
          <div style={{
            padding: '10px',
            backgroundColor: testResult.includes('✅') ? '#d4edda' : '#f8d7da',
            border: `1px solid ${testResult.includes('✅') ? '#c3e6cb' : '#f5c6cb'}`,
            borderRadius: '4px',
            color: testResult.includes('✅') ? '#155724' : '#721c24'
          }}>
            {testResult}
          </div>
        )}
      </div>
    </div>
  );
};

export default DiagnosticPanel;
