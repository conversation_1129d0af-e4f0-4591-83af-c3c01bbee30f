
import React from 'react';
import { useNavigate } from 'react-router-dom';
import { useLanguage } from '@/hooks/useLanguage';
import Header from '@/components/Header';
import Footer from '@/components/Footer';

interface ListingNotFoundProps {
  isMobile: boolean;
}

const ListingNotFound: React.FC<ListingNotFoundProps> = ({ isMobile }) => {
  const { t } = useLanguage();
  const navigate = useNavigate();
  
  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      <main className={`flex-grow container mx-auto px-4 py-8 ${isMobile ? 'pt-20' : 'pt-24'}`}>
        <div className="flex justify-center items-center h-64">
          <div className="text-center">
            <h2 className="text-xl font-bold mb-2">{t('listings.listingNotFound')}</h2>
            <p className="text-muted-foreground">{t('listings.listingMayHaveBeenRemoved')}</p>
            <button
              onClick={() => navigate('/listings')}
              className="mt-4 text-primary hover:underline"
            >
              {t('listings.backToListings')}
            </button>
          </div>
        </div>
      </main>
      <Footer />
    </div>
  );
};

export default ListingNotFound;
