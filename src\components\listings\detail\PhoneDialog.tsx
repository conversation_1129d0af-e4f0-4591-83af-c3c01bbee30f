
import React, { useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogFooter } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Phone } from "lucide-react";
import { useLanguage } from "@/hooks/useLanguage";
import { useNavigate } from "react-router-dom";

interface PhoneDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  sellerPhone: string;
  isAuthenticated?: boolean;
}

const PhoneDialog: React.FC<PhoneDialogProps> = ({
  open,
  onOpenChange,
  sellerPhone,
  isAuthenticated = true
}) => {
  const { t } = useLanguage();
  const navigate = useNavigate();

  // Проверим переводы
  console.log("Translation test in PhoneDialog:");
  console.log("listings.sellerPhone:", t('listings.sellerPhone'));
  console.log("listings.youCanCallSeller:", t('listings.youCanCallSeller'));
  console.log("listings.callNow:", t('listings.callNow'));

  // Логируем состояние, чтобы увидеть значения при рендеринге
  useEffect(() => {
    console.log("PhoneDialog rendered with:", { open, isAuthenticated, sellerPhone });
  }, [open, isAuthenticated, sellerPhone]);

  // Перенаправление на страницу авторизации, если пользователь не авторизован при открытии диалога
  useEffect(() => {
    console.log("PhoneDialog - open:", open, "isAuthenticated:", isAuthenticated);
    // Всегда показываем диалог, независимо от статуса авторизации
    // Закомментировали проверку авторизации
    /*
    if (open && !isAuthenticated) {
      onOpenChange(false);
      navigate('/auth?returnUrl=' + encodeURIComponent(window.location.pathname));
    }
    */
  }, [open, isAuthenticated, navigate, onOpenChange]);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>{t('listings.sellerPhone')}</DialogTitle>
        </DialogHeader>
        <div className="flex items-center justify-center py-6">
          <div className="text-center">
            <Phone className="h-12 w-12 mx-auto mb-4 text-primary" />
            <p className="text-xl font-bold">{sellerPhone || "+7 (XXX) XXX-XX-XX"}</p>
            <p className="text-muted-foreground mt-2">{t('listings.youCanCallSeller')}</p>
          </div>
        </div>
        <DialogFooter>
          <Button
            onClick={() => window.open(`tel:${sellerPhone}`, '_blank')}
            className="w-full"
          >
            {t('listings.callNow')}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default PhoneDialog;
