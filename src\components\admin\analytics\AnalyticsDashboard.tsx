import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { useLanguage } from '@/hooks/useLanguage';
import { AnalyticsData, AnalyticsFilters } from '@/types/admin';
import { Download, TrendingUp, Users, FileText, Calendar } from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';

const AnalyticsDashboard: React.FC = () => {
  const { t } = useLanguage();
  const { toast } = useToast();
  const [data, setData] = useState<AnalyticsData | null>(null);
  const [filters, setFilters] = useState<AnalyticsFilters>({
    period: 'month'
  });
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    fetchAnalyticsData();
  }, [filters]);

  const fetchAnalyticsData = async () => {
    setIsLoading(true);
    try {
      // Fetch total listings
      const { count: totalListings } = await supabase
        .from('listings')
        .select('*', { count: 'exact', head: true });

      // Fetch listings by status
      const { data: statusData } = await supabase
        .from('listings')
        .select('status')
        .not('status', 'is', null);

      const listingsByStatus = statusData?.reduce((acc, item) => {
        const status = item.status || 'pending';
        acc[status] = (acc[status] || 0) + 1;
        return acc;
      }, {} as Record<string, number>) || {};

      // Fetch new listings for different periods
      const now = new Date();
      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
      const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
      const monthAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);

      const { count: newToday } = await supabase
        .from('listings')
        .select('*', { count: 'exact', head: true })
        .gte('created_at', today.toISOString());

      const { count: newThisWeek } = await supabase
        .from('listings')
        .select('*', { count: 'exact', head: true })
        .gte('created_at', weekAgo.toISOString());

      const { count: newThisMonth } = await supabase
        .from('listings')
        .select('*', { count: 'exact', head: true })
        .gte('created_at', monthAgo.toISOString());

      // Fetch active users (users who created listings in the last month)
      const { data: activeUsersData } = await supabase
        .from('listings')
        .select('user_id')
        .gte('created_at', monthAgo.toISOString());

      const activeUsers = new Set(activeUsersData?.map(item => item.user_id)).size;

      // Fetch top categories (by make)
      const { data: categoriesData } = await supabase
        .from('listings')
        .select('make')
        .not('make', 'is', null);

      const topCategories = Object.entries(
        categoriesData?.reduce((acc, item) => {
          acc[item.make] = (acc[item.make] || 0) + 1;
          return acc;
        }, {} as Record<string, number>) || {}
      )
        .sort(([, a], [, b]) => b - a)
        .slice(0, 5)
        .map(([category, count]) => ({ category, count }));

      setData({
        totalListings: totalListings || 0,
        listingsByStatus: {
          pending: listingsByStatus.pending || 0,
          approved: listingsByStatus.approved || 0,
          rejected: listingsByStatus.rejected || 0,
          blocked: listingsByStatus.blocked || 0,
          active: listingsByStatus.active || 0
        },
        newListingsToday: newToday || 0,
        newListingsThisWeek: newThisWeek || 0,
        newListingsThisMonth: newThisMonth || 0,
        activeUsers,
        topCategories
      });
    } catch (error) {
      console.error('Error fetching analytics:', error);
      toast({
        title: t('admin.exportError'),
        description: 'Failed to fetch analytics data',
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
    }
  };

  const exportData = async (format: 'csv' | 'excel') => {
    try {
      // Simple CSV export implementation
      if (format === 'csv' && data) {
        const csvContent = [
          ['Metric', 'Value'],
          ['Total Listings', data.totalListings],
          ['Pending', data.listingsByStatus.pending],
          ['Approved', data.listingsByStatus.approved],
          ['Rejected', data.listingsByStatus.rejected],
          ['Blocked', data.listingsByStatus.blocked],
          ['Active', data.listingsByStatus.active],
          ['New Today', data.newListingsToday],
          ['New This Week', data.newListingsThisWeek],
          ['New This Month', data.newListingsThisMonth],
          ['Active Users', data.activeUsers],
          ...data.topCategories.map(cat => [`Top Category: ${cat.category}`, cat.count])
        ].map(row => row.join(',')).join('\n');

        const blob = new Blob([csvContent], { type: 'text/csv' });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `analytics-${new Date().toISOString().split('T')[0]}.csv`;
        a.click();
        window.URL.revokeObjectURL(url);

        toast({
          title: t('admin.exportSuccess'),
          description: 'Analytics data exported successfully'
        });
      }
    } catch (error) {
      toast({
        title: t('admin.exportError'),
        description: 'Failed to export data',
        variant: 'destructive'
      });
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p>{t('admin.loading')}</p>
        </div>
      </div>
    );
  }

  if (!data) {
    return (
      <div className="text-center py-8 text-muted-foreground">
        {t('admin.noData')}
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold">{t('admin.analytics')}</h2>
          <p className="text-muted-foreground">{t('admin.analyticsOverview')}</p>
        </div>
        <div className="flex gap-2">
          <Select
            value={filters.period}
            onValueChange={(value) => setFilters({ ...filters, period: value as any })}
          >
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="day">{t('admin.periodDay')}</SelectItem>
              <SelectItem value="week">{t('admin.periodWeek')}</SelectItem>
              <SelectItem value="month">{t('admin.periodMonth')}</SelectItem>
              <SelectItem value="year">{t('admin.periodYear')}</SelectItem>
            </SelectContent>
          </Select>
          <Button variant="outline" onClick={() => exportData('csv')}>
            <Download className="w-4 h-4 mr-2" />
            {t('admin.exportCsv')}
          </Button>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t('admin.totalListings')}</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{data.totalListings}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t('admin.activeUsers')}</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{data.activeUsers}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t('admin.newToday')}</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{data.newListingsToday}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{t('admin.newThisMonth')}</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{data.newListingsThisMonth}</div>
          </CardContent>
        </Card>
      </div>

      {/* Status Distribution */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>{t('admin.listingsByStatus')}</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {Object.entries(data.listingsByStatus).map(([status, count]) => (
                <div key={status} className="flex justify-between items-center">
                  <span className="capitalize">{t(`admin.${status}`)}</span>
                  <span className="font-semibold">{count}</span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>{t('admin.topCategories')}</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {data.topCategories.map((category, index) => (
                <div key={category.category} className="flex justify-between items-center">
                  <span>{category.category}</span>
                  <span className="font-semibold">{category.count}</span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default AnalyticsDashboard;
