
import React, { useState, useEffect } from 'react';
import { useLanguage } from '@/hooks/useLanguage';
import { useAuth } from '@/contexts/AuthContext';
import { supabase } from '@/integrations/supabase/client';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ExternalLink, Trash2 } from 'lucide-react';
import { Switch } from '@/components/ui/switch';
import { Skeleton } from '@/components/ui/skeleton';
import { toast } from 'sonner';
import { useNavigate } from 'react-router-dom';

interface SavedSearch {
  id: string;
  user_id: string;
  name: string;
  search_term: string | null;
  filters: Record<string, any> | null;
  notify: boolean;
  created_at: string;
}

const SavedSearches: React.FC = () => {
  const { t } = useLanguage();
  const { user } = useAuth();
  const navigate = useNavigate();
  const [savedSearches, setSavedSearches] = useState<SavedSearch[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchSavedSearches = async () => {
      if (!user) return;
      
      try {
        setIsLoading(true);
        
        // Use type assertion to bypass TypeScript checks
        const { data, error } = await (supabase
          .from('saved_searches') as any)
          .select('*')
          .eq('user_id', user.id)
          .order('created_at', { ascending: false });
        
        if (error) throw error;
        
        setSavedSearches(data || []);
      } catch (error) {
        console.error('Error fetching saved searches:', error);
        toast.error(t('errorFetchingSearches'));
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchSavedSearches();
  }, [user, t]);

  const handleToggleNotifications = async (id: string, currentValue: boolean) => {
    try {
      // Use type assertion to bypass TypeScript checks
      const { error } = await (supabase
        .from('saved_searches') as any)
        .update({ notify: !currentValue })
        .eq('id', id);
      
      if (error) throw error;
      
      setSavedSearches(searches => 
        searches.map(search => 
          search.id === id ? { ...search, notify: !currentValue } : search
        )
      );
      
      toast.success(currentValue ? t('notificationsDisabled') : t('notificationsEnabled'));
    } catch (error) {
      console.error('Error updating notification status:', error);
      toast.error(t('errorUpdatingNotifications'));
    }
  };

  const handleDeleteSearch = async (id: string) => {
    try {
      // Use type assertion to bypass TypeScript checks
      const { error } = await (supabase
        .from('saved_searches') as any)
        .delete()
        .eq('id', id);
      
      if (error) throw error;
      
      setSavedSearches(searches => searches.filter(search => search.id !== id));
      toast.success(t('searchDeleted'));
    } catch (error) {
      console.error('Error deleting saved search:', error);
      toast.error(t('errorDeletingSearch'));
    }
  };

  const handleRunSearch = (search: SavedSearch) => {
    const params = new URLSearchParams();
    
    if (search.search_term) {
      params.set('location', search.search_term);
    }
    
    // Add any other filter params from the filters object
    if (search.filters) {
      Object.entries(search.filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          params.set(key, String(value));
        }
      });
    }
    
    navigate(`/listings?${params.toString()}`);
  };
  
  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>{t('savedSearches')}</CardTitle>
          <CardDescription>{t('savedSearchesDescription')}</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[1, 2, 3].map((i) => (
              <div key={i} className="flex items-center justify-between border-b pb-4">
                <div>
                  <Skeleton className="h-5 w-[200px] mb-2" />
                  <Skeleton className="h-4 w-[120px]" />
                </div>
                <div className="flex space-x-2">
                  <Skeleton className="h-9 w-9 rounded-md" />
                  <Skeleton className="h-9 w-9 rounded-md" />
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>{t('savedSearches')}</CardTitle>
        <CardDescription>{t('savedSearchesDescription')}</CardDescription>
      </CardHeader>
      <CardContent>
        {savedSearches.length === 0 ? (
          <div className="text-center py-8">
            <p className="text-muted-foreground">{t('noSavedSearches')}</p>
          </div>
        ) : (
          <div className="space-y-4">
            {savedSearches.map((search) => (
              <div key={search.id} className="flex items-center justify-between border-b pb-4 last:border-0">
                <div>
                  <h3 className="font-medium">{search.name}</h3>
                  <p className="text-sm text-muted-foreground">
                    {search.search_term || t('allListings')}
                  </p>
                </div>
                <div className="flex items-center space-x-2">
                  <Switch
                    checked={search.notify}
                    onCheckedChange={() => handleToggleNotifications(search.id, search.notify)}
                    aria-label={search.notify ? t('disableNotifications') : t('enableNotifications')}
                  />
                  
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => handleRunSearch(search)}
                  >
                    <ExternalLink className="h-4 w-4" />
                    <span className="sr-only">{t('runSearch')}</span>
                  </Button>
                  
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => handleDeleteSearch(search.id)}
                  >
                    <Trash2 className="h-4 w-4 text-destructive" />
                    <span className="sr-only">{t('deleteSearch')}</span>
                  </Button>
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default SavedSearches;
