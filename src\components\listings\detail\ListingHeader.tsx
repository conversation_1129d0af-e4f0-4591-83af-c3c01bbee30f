
import React from 'react';
import { useLanguage } from '@/hooks/useLanguage';
import ListingDetailActions from '@/components/listings/ListingDetailActions';
import BackButton from '@/components/BackButton';

interface ListingHeaderProps {
  title: string;
  make: string;
  model: string;
  year: number;
  isOwner: boolean;
  listingId: string;
  userId: string;
  onDelete: () => void;
  isDeleting: boolean;
}

const ListingHeader: React.FC<ListingHeaderProps> = ({
  title,
  make,
  model,
  year,
  isOwner,
  listingId,
  userId,
  onDelete,
  isDeleting
}) => {
  const { t } = useLanguage();
  
  // Pre-translate text for proper rendering
  const backToListingsText = t('listings.backToListings');

  return (
    <div className="mb-6">
      <BackButton to="/listings" text={backToListingsText} />

      <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center gap-4">
        <div>
          <h1 className="text-2xl md:text-3xl font-bold">{title}</h1>
          <div className="text-muted-foreground mt-1">
            {make} {model} ({year})
          </div>
        </div>

        {isOwner && (
          <div className="w-full lg:w-auto">
            <ListingDetailActions
              listingId={listingId}
              sellerId={userId}
              onDelete={onDelete}
              isDeleting={isDeleting}
            />
          </div>
        )}
      </div>
    </div>
  );
};

export default ListingHeader;
