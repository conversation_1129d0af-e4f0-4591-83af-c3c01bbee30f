
import React, { useState, useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { useLanguage } from '@/hooks/useLanguage';
import { useAuth } from '@/contexts/AuthContext';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Mail, Lock, User } from 'lucide-react';
import { toast } from 'sonner';
import { Link } from 'react-router-dom';
import GoogleSignInButton from './GoogleSignInButton';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

const AuthForm: React.FC = () => {
  const { t } = useLanguage();
  const { signIn, signUp, isAuthenticated, isLoading } = useAuth();
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const initialMode = searchParams.get('mode') === 'signup' ? 'signup' : 'signin';
  
  const [mode, setMode] = useState<'signin' | 'signup'>(initialMode);
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');
  
  useEffect(() => {
    if (isAuthenticated) {
      navigate('/');
    }
  }, [isAuthenticated, navigate]);
  
  useEffect(() => {
    // Update mode if URL params change
    const currentMode = searchParams.get('mode') === 'signup' ? 'signup' : 'signin';
    setMode(currentMode);
  }, [searchParams]);
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (mode === 'signin') {
      if (!email || !password) {
        toast.error(t('auth.allFieldsRequired'), {
          description: t('auth.pleaseEnterEmailAndPassword')
        });
        return;
      }
    } else {
      // Signup mode - check all required fields
      if (!email || !password || !firstName || !lastName) {
        toast.error(t('auth.allFieldsRequired'), {
          description: t('auth.pleaseEnterAllFields')
        });
        return;
      }
    }
    
    try {
      if (mode === 'signin') {
        await signIn(email, password);
      } else {
        await signUp(email, password, firstName, lastName);
        toast.success(t('auth.accountCreated'), {
          description: t('auth.youCanNowSignIn')
        });
        setMode('signin');
      }
    } catch (error: any) {
      console.error('Authentication error:', error);
      toast.error(t('auth.authError'), {
        description: error.message || t('auth.unknownError')
      });
    }
  };
  
  return (
    <div className="bg-card border rounded-xl p-6 shadow-subtle">
      <div className="text-center mb-6">
        <h1 className="text-2xl font-bold">
          {mode === 'signin' ? t('signIn') : t('signUp')}
        </h1>
        <p className="text-muted-foreground mt-2">
          {mode === 'signin' ? t('auth.welcomeBack') : t('auth.createAccount')}
        </p>
      </div>
      
      <Tabs 
        value={mode} 
        onValueChange={(v) => setMode(v as 'signin' | 'signup')}
        className="mb-6"
      >
        <TabsList className="grid grid-cols-2">
          <TabsTrigger value="signin">{t('signIn')}</TabsTrigger>
          <TabsTrigger value="signup">{t('signUp')}</TabsTrigger>
        </TabsList>
      </Tabs>
      
      <form onSubmit={handleSubmit} className="space-y-4">
        {mode === 'signup' && (
          <>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="firstName">{t('firstName')}</Label>
                <div className="relative">
                  <Input 
                    id="firstName" 
                    type="text" 
                    placeholder={t('firstName')} 
                    value={firstName}
                    onChange={(e) => setFirstName(e.target.value)}
                    className="pl-10" 
                  />
                  <User className="absolute left-3 top-2.5 h-5 w-5 text-muted-foreground" />
                </div>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="lastName">{t('lastName')}</Label>
                <div className="relative">
                  <Input 
                    id="lastName" 
                    type="text" 
                    placeholder={t('lastName')} 
                    value={lastName}
                    onChange={(e) => setLastName(e.target.value)}
                    className="pl-10" 
                  />
                  <User className="absolute left-3 top-2.5 h-5 w-5 text-muted-foreground" />
                </div>
              </div>
            </div>
          </>
        )}
        
        <div className="space-y-2">
          <Label htmlFor="email">{t('email')}</Label>
          <div className="relative">
            <Input 
              id="email" 
              type="email" 
              placeholder="<EMAIL>" 
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className="pl-10" 
            />
            <Mail className="absolute left-3 top-2.5 h-5 w-5 text-muted-foreground" />
          </div>
        </div>
        
        <div className="space-y-2">
          <div className="flex justify-between items-center">
            <Label htmlFor="password">{t('password')}</Label>
            {mode === 'signin' && (
              <Link 
                to="/forgot-password" 
                className="text-sm text-primary hover:underline"
              >
                {t('auth.forgotPassword')}
              </Link>
            )}
          </div>
          <div className="relative">
            <Input 
              id="password" 
              type="password" 
              placeholder="••••••••" 
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              className="pl-10" 
            />
            <Lock className="absolute left-3 top-2.5 h-5 w-5 text-muted-foreground" />
          </div>
        </div>
        
        <Button className="w-full" type="submit" disabled={isLoading}>
          {isLoading 
            ? (mode === 'signin' ? t('auth.signingIn') : t('auth.signingUp')) 
            : (mode === 'signin' ? t('signIn') : t('signUp'))}
        </Button>
      </form>
      
      <div className="relative my-6">
        <div className="absolute inset-0 flex items-center">
          <div className="w-full border-t border-border"></div>
        </div>
        <div className="relative flex justify-center text-xs uppercase">
          <span className="bg-card px-2 text-muted-foreground">
            {t('auth.orContinueWith')}
          </span>
        </div>
      </div>
      
      <GoogleSignInButton />
      
      <div className="text-center text-sm text-muted-foreground mt-6">
        {mode === 'signin' ? (
          <>
            <span>{t('auth.dontHaveAccount')} </span>
            <Link 
              to="/auth?mode=signup" 
              className="text-primary hover:underline"
            >
              {t('signUp')}
            </Link>
          </>
        ) : (
          <>
            <span>{t('auth.alreadyHaveAccount')} </span>
            <Link 
              to="/auth" 
              className="text-primary hover:underline"
            >
              {t('signIn')}
            </Link>
          </>
        )}
      </div>
    </div>
  );
};

export default AuthForm;
