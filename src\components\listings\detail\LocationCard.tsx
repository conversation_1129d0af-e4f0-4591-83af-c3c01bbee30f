
import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { useLanguage } from '@/hooks/useLanguage';

interface LocationCardProps {
  location: string;
}

const LocationCard: React.FC<LocationCardProps> = ({ location }) => {
  const { t } = useLanguage();
  
  return (
    <Card>
      <CardHeader className="pb-3">
        <CardTitle>{t('listings.location')}</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="font-medium mb-3">{location}</div>
        <div className="h-[200px] w-full rounded-md overflow-hidden relative">
          <iframe
            title="location map"
            width="100%"
            height="100%"
            frameBorder="0"
            scrolling="no"
            marginHeight={0}
            marginWidth={0}
            src={`https://www.openstreetmap.org/export/embed.html?bbox=-74.0060%2C40.7128%2C-73.9860%2C40.7328&layer=mapnik&marker=40.7128%2C-74.0060`}
            style={{ border: 0 }}
          />
        </div>
      </CardContent>
    </Card>
  );
};

export default LocationCard;
