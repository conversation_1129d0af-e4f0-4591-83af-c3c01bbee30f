
import React from 'react';
import { useLanguage } from '@/hooks/useLanguage';
import { Button } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { RefreshCcw, BadgeCheck, BadgeDollarSign } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { ToggleGroup, ToggleGroupItem } from '@/components/ui/toggle-group';

interface ListingFiltersProps {
  featureFilter: string;
  setFeatureFilter: (value: string) => void;
  vipFilter?: string;
  setVipFilter?: (value: string) => void;
  paidFilter?: string;
  setPaidFilter?: (value: string) => void;
  onRefresh: () => Promise<void>;
}

const ListingFilters: React.FC<ListingFiltersProps> = ({ 
  featureFilter, 
  setFeatureFilter,
  vipFilter = 'all',
  setVipFilter = () => {},
  paidFilter = 'all',
  setPaidFilter = () => {},
  onRefresh 
}) => {
  const { language } = useLanguage();
  const { t } = useTranslation();
  
  return (
    <div className="flex flex-col md:flex-row gap-3">
      <div className="flex gap-2 flex-wrap">
        <Select value={featureFilter} onValueChange={setFeatureFilter}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder={t('all')} />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">{t('all')}</SelectItem>
            <SelectItem value="featured">
              {language === 'ru' ? 'Избранное' : t('featured')}
            </SelectItem>
            <SelectItem value="not_featured">
              {language === 'ru' ? 'Не избранное' : t('notFeatured')}
            </SelectItem>
          </SelectContent>
        </Select>
        
        <Select value={vipFilter} onValueChange={setVipFilter}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder={t('all')} />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">{t('all')}</SelectItem>
            <SelectItem value="vip">
              <div className="flex items-center">
                <BadgeCheck className="mr-2 h-4 w-4 text-blue-500" />
                {language === 'ru' ? 'VIP статус' : 'VIP Status'}
              </div>
            </SelectItem>
            <SelectItem value="not_vip">
              {language === 'ru' ? 'Без VIP статуса' : 'Not VIP'}
            </SelectItem>
          </SelectContent>
        </Select>
        
        <Select value={paidFilter} onValueChange={setPaidFilter}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder={t('all')} />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">{t('all')}</SelectItem>
            <SelectItem value="paid">
              <div className="flex items-center">
                <BadgeDollarSign className="mr-2 h-4 w-4 text-green-500" />
                {language === 'ru' ? 'Оплачено' : 'Paid'}
              </div>
            </SelectItem>
            <SelectItem value="not_paid">
              {language === 'ru' ? 'Не оплачено' : 'Not Paid'}
            </SelectItem>
          </SelectContent>
        </Select>
      </div>
      
      <Button variant="default" className="bg-blue-600 hover:bg-blue-700 ml-auto" onClick={onRefresh}>
        <RefreshCcw className="h-4 w-4 mr-2" />
        {language === 'ru' ? 'Обновить' : t('refresh')}
      </Button>
    </div>
  );
};

export default ListingFilters;
