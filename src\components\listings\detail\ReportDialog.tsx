
import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON><PERSON>, Di<PERSON>Footer } from "@/components/ui/dialog";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { useLanguage } from "@/hooks/useLanguage";
import { toast } from "sonner";
import { useNavigate } from "react-router-dom";

// Importing supabase client directly since we're having type issues with the reports table
import { supabase } from "@/integrations/supabase/client";

interface ReportDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  listingId: string;
  userId: string | undefined;
  isAuthenticated: boolean;
}

const ReportDialog: React.FC<ReportDialogProps> = ({
  open,
  onOpenChange,
  listingId,
  userId,
  isAuthenticated
}) => {
  const { t } = useLanguage();
  const navigate = useNavigate();
  const [reason, setReason] = useState('');
  const [isSending, setIsSending] = useState(false);

  const handleSendReport = async () => {
    // Check authentication
    if (!isAuthenticated) {
      navigate('/auth?returnUrl=' + encodeURIComponent(window.location.pathname));
      return;
    }

    // Check for user ID
    if (!userId) {
      toast.error(t('auth.authenticationRequired'));
      return;
    }

    // Check for reason
    if (!reason.trim()) {
      toast.error(t('listings.reportReasonRequired'));
      return;
    }

    try {
      setIsSending(true);

      // Use functions.invoke method with type assertion to bypass TS errors
      const { error } = await supabase.functions.invoke("submit-report", {
        body: {
          userId,
          listingId,
          reason: reason.trim()
        }
      });

      if (error) {
        console.error("Report submission error:", error);
        
        if (error.message.includes('duplicate')) {
          toast.error(t('listings.reportAlreadySent'));
        } else if (error.message.includes('foreign key')) {
          toast.error(t('listings.reportInvalidListing'));
        } else {
          toast.error(t('listings.reportError'));
        }
        
        setIsSending(false);
        return;
      }

      // Success
      toast.success(t('listings.reportSent'), {
        description: t('listings.reportSentDescription')
      });

      setReason('');
      onOpenChange(false);
    } catch (error) {
      console.error("Error sending report:", error);
      toast.error(t('listings.reportError'));
    } finally {
      setIsSending(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>{t('listings.reportListing')}</DialogTitle>
        </DialogHeader>
        <div className="space-y-4 py-4">
          <p className="text-sm text-muted-foreground">
            {t('listings.reportListingDescription')}
          </p>
          <Textarea
            value={reason}
            onChange={(e) => setReason(e.target.value)}
            placeholder={t('listings.reportReasonPlaceholder')}
            rows={5}
          />
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            {t('ui.cancel')}
          </Button>
          <Button
            onClick={handleSendReport}
            disabled={!reason.trim() || isSending}
          >
            {isSending ? t('listings.sending') : t('ui.submit')}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default ReportDialog;
