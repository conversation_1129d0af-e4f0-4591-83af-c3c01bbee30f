
import React from 'react';
import { useLanguage } from '@/hooks/useLanguage';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';

interface InputFileUploadProps {
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  helpText?: string;
}

const InputFileUpload: React.FC<InputFileUploadProps> = ({
  onChange,
  helpText
}) => {
  const { t } = useLanguage();
  
  return (
    <div className="space-y-2">
      <Label htmlFor="images">{t('images')}</Label>
      <Input 
        id="images" 
        type="file" 
        accept="image/*" 
        multiple
        onChange={onChange}
      />
      {helpText && (
        <div className="text-xs text-muted-foreground mt-1">
          {helpText}
        </div>
      )}
    </div>
  );
};

export default InputFileUpload;
