
import React from 'react';
import { useLanguage } from '@/hooks/useLanguage';
import { FormField, FormItem } from '@/components/ui/form';
import ImageUploader from '../ImageUploader';
import { useFormContext } from './ListingFormContext';

const ImagesFormSection: React.FC = () => {
  const { t } = useLanguage();
  const { form } = useFormContext();
  
  return (
    <FormField
      control={form.control}
      name="images"
      render={({ field }) => (
        <FormItem>
          <ImageUploader 
            images={field.value} 
            setImages={field.onChange}
            maxImages={5}
          />
        </FormItem>
      )}
    />
  );
};

export default ImagesFormSection;
