export const carMakes = [
  "Acura",
  "Alfa Romeo",
  "Aston Martin",
  "Audi",
  "Bentley",
  "BMW",
  "Bugatti",
  "Buick",
  "Cadillac",
  "Chevrolet",
  "Chrysler",
  "Citroën",
  "Dodge",
  "Ferrari",
  "Fiat",
  "Ford",
  "Genesis",
  "GMC",
  "Honda",
  "Hyundai",
  "Infiniti",
  "Jaguar",
  "Jeep",
  "Kia",
  "Lamborghini",
  "Land Rover",
  "Lexus",
  "Lincoln",
  "Lotus",
  "Maserati",
  "Mazda",
  "McLaren",
  "Mercedes-Benz",
  "MINI",
  "Mitsubishi",
  "Nissan",
  "Pagani",
  "Peugeot",
  "Porsche",
  "RAM",
  "Renault",
  "Rolls-Royce",
  "Subaru",
  "Suzuki",
  "Tesla",
  "Toyota",
  "Volkswagen",
  "Volvo"
];

// Models by make
export const carModelsByMake: Record<string, string[]> = {
  "BMW": [
    "1 Series",
    "2 Series",
    "3 Series",
    "4 Series",
    "5 Series",
    "6 Series",
    "7 Series",
    "8 Series",
    "X1",
    "X2",
    "X3",
    "X4",
    "X5",
    "X6",
    "X7",
    "Z4",
    "i3",
    "i4",
    "i8",
    "iX"
  ],
  "Mercedes-Benz": [
    "A-Class",
    "B-Class",
    "C-Class",
    "CLA",
    "CLS",
    "E-Class",
    "G-Class",
    "GLA",
    "GLB",
    "GLC",
    "GLE",
    "GLS",
    "S-Class",
    "SL",
    "SLC",
    "AMG GT",
    "EQA",
    "EQB",
    "EQC",
    "EQE",
    "EQS"
  ],
  "Toyota": [
    "Avalon",
    "Camry",
    "Corolla",
    "Prius",
    "Yaris",
    "86",
    "Supra",
    "RAV4",
    "Highlander",
    "4Runner",
    "Land Cruiser",
    "Sequoia",
    "C-HR",
    "Venza",
    "Sienna",
    "Tacoma",
    "Tundra",
    "bZ4X"
  ],
  "Ford": [
    "Fiesta",
    "Focus",
    "Fusion",
    "Mustang",
    "GT",
    "EcoSport",
    "Escape",
    "Edge",
    "Explorer",
    "Expedition",
    "Bronco",
    "Bronco Sport",
    "Ranger",
    "F-150",
    "F-250",
    "F-350",
    "Transit",
    "Maverick",
    "Mustang Mach-E"
  ],
  "Audi": [
    "A1",
    "A3",
    "A4",
    "A5",
    "A6",
    "A7",
    "A8",
    "Q2",
    "Q3",
    "Q4 e-tron",
    "Q5",
    "Q7",
    "Q8",
    "TT",
    "R8",
    "e-tron",
    "e-tron GT"
  ]
};

// Default models for cars not in the specific mapping
export const defaultModels = [
  "Sedan",
  "Coupe",
  "Convertible",
  "SUV",
  "Crossover",
  "Hatchback",
  "Wagon",
  "Minivan",
  "Pickup",
  "Sports Car",
  "Luxury"
];

// Multilingual dictionaries for car filters
export const carBodyTypes = {
  ru: [
    "Седан",
    "Хэтчбек",
    "Универсал",
    "Внедорожник",
    "Кроссовер",
    "Минивэн",
    "Купе",
    "Кабриолет",
    "Пикап",
    "Фургон",
    "Лимузин"
  ],
  en: [
    "Sedan",
    "Hatchback",
    "Wagon",
    "SUV",
    "Crossover",
    "Minivan",
    "Coupe",
    "Convertible",
    "Pickup",
    "Van",
    "Limousine"
  ]
};

export const carFuelTypes = {
  ru: [
    "Бензин",
    "Дизель",
    "Электро",
    "Гибрид",
    "Газ",
    "Газ/Бензин"
  ],
  en: [
    "Gasoline",
    "Diesel",
    "Electric",
    "Hybrid",
    "Gas",
    "Gas/Gasoline"
  ]
};

export const carTransmissionTypes = {
  ru: [
    "АКПП",
    "МКПП",
    "Вариатор",
    "Робот"
  ],
  en: [
    "Automatic",
    "Manual",
    "CVT",
    "Robot"
  ]
};

export const carColors = {
  ru: [
    "Белый",
    "Черный",
    "Серый",
    "Серебристый",
    "Красный",
    "Синий",
    "Зеленый",
    "Коричневый",
    "Бежевый",
    "Золотистый",
    "Оранжевый",
    "Фиолетовый",
    "Желтый"
  ],
  en: [
    "White",
    "Black",
    "Gray",
    "Silver",
    "Red",
    "Blue",
    "Green",
    "Brown",
    "Beige",
    "Gold",
    "Orange",
    "Purple",
    "Yellow"
  ]
};
