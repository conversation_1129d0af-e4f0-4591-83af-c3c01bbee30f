# Error Handling Test Plan

## Test Scenarios for Duplicate Error Messages Fix

### 1. **Missing Required Fields Test**
- Navigate to Create Listing page
- Leave title field empty
- Try to submit form
- **Expected**: Single error message about missing title

### 2. **Invalid Price Test**
- Fill in title, make, model, location
- Enter negative price (e.g., -100)
- Try to submit form
- **Expected**: Single error message about invalid price

### 3. **Invalid Year Test**
- Fill in required fields
- Enter invalid year (e.g., 1800 or 2030)
- Try to submit form
- **Expected**: Single error message about invalid year

### 4. **Backend Database Error Test**
- Fill in all fields correctly
- Simulate database error (if possible)
- **Expected**: Single error message from backend

### 5. **Success Case Test**
- Fill in all required fields correctly
- Submit form
- **Expected**: Single success message

## Before Fix Issues:
- Two error messages appeared:
  1. "Ошибка при создании объявления. Пожалуйста, попробуйте еще раз или свяжитесь с поддержкой"
  2. "Ошибка при создании объявления"

## After Fix Expected:
- Only one appropriate error message should appear
- Error messages should be in Russian
- Error messages should be helpful and specific
