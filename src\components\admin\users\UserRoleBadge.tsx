
import React from 'react';
import { useLanguage } from '@/hooks/useLanguage';
import { Badge } from '@/components/ui/badge';

interface UserRoleBadgeProps {
  role: string;
}

const UserRoleBadge: React.FC<UserRoleBadgeProps> = ({ role }) => {
  const { t } = useLanguage();
  
  if (role === 'admin') {
    return <Badge className="bg-primary">{t('admin')}</Badge>;
  } else if (role === 'moderator') {
    return <Badge className="bg-blue-500">{t('moderator')}</Badge>;
  } else {
    return <Badge variant="outline">{t('regular')}</Badge>;
  }
};

export default UserRoleBadge;
