
import React, { useState, useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import { useLanguage } from '@/hooks/useLanguage';
import Logo from '@/components/header/Logo';
import DesktopNavigation from '@/components/header/DesktopNavigation';
import DesktopActions from '@/components/header/DesktopActions';
import MobileActions from '@/components/header/MobileActions';
import MobileMenu from '@/components/header/MobileMenu';

const Header: React.FC = () => {
  const { t } = useLanguage();
  const location = useLocation();
  const [scrolled, setScrolled] = useState(false);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      setScrolled(window.scrollY > 10);
    };

    window.addEventListener('scroll', handleScroll);
    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);

  // Close mobile menu when route changes
  useEffect(() => {
    setMobileMenuOpen(false);
  }, [location.pathname]);

  const toggleMobileMenu = () => setMobileMenuOpen(!mobileMenuOpen);

  return (
    <>
      <header 
        className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${
          scrolled 
            ? 'bg-white/70 dark:bg-black/70 backdrop-blur-lg shadow-subtle' 
            : 'bg-transparent'
        }`}
      >
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between min-h-16 md:h-20">
            <div className="flex-shrink-0">
              <Logo />
            </div>
            <div className="flex-1 ml-6">
              <DesktopNavigation />
            </div>
            <DesktopActions />
            <MobileActions 
              mobileMenuOpen={mobileMenuOpen} 
              toggleMobileMenu={toggleMobileMenu}
            />
          </div>
        </div>

        <MobileMenu isOpen={mobileMenuOpen} />
      </header>
      {/* Add spacer to prevent content from being hidden under the fixed header */}
      <div className="h-20 md:h-20 w-full"></div>
    </>
  );
};

export default Header;
