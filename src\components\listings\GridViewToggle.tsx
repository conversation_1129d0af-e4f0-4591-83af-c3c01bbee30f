
import React from 'react';
import { Button } from '@/components/ui/button';
import { Grid, LayoutGrid, List } from 'lucide-react';
import { useLanguage } from '@/hooks/useLanguage';
import { useIsMobile } from '@/hooks/use-mobile';

export type GridViewType = 'single' | 'multiple' | 'list';

interface GridViewToggleProps {
  currentView: GridViewType;
  onChange: (view: GridViewType) => void;
  className?: string;
}

const GridViewToggle: React.FC<GridViewToggleProps> = ({
  currentView,
  onChange,
  className = ''
}) => {
  const { t } = useLanguage();
  const isMobile = useIsMobile();

  // On mobile, use larger touch-friendly buttons
  const buttonSize = isMobile ? "default" : "sm";
  const buttonClass = isMobile ? "h-10 w-10 p-0" : "h-8 w-8 p-0";
  const iconClass = isMobile ? "h-5 w-5" : "h-4 w-4";

  return (
    <div className={`flex items-center gap-1 ${className}`}>
      <Button
        variant={currentView === 'multiple' ? 'default' : 'outline'}
        size={buttonSize}
        onClick={() => onChange('multiple')}
        title={t('listings.multiColumnView')}
        className={buttonClass}
      >
        <LayoutGrid className={iconClass} />
      </Button>

      <Button
        variant={currentView === 'single' ? 'default' : 'outline'}
        size={buttonSize}
        onClick={() => onChange('single')}
        title={t('listings.singleColumnView')}
        className={buttonClass}
      >
        <Grid className={iconClass} />
      </Button>

      <Button
        variant={currentView === 'list' ? 'default' : 'outline'}
        size={buttonSize}
        onClick={() => onChange('list')}
        title={t('listings.listView')}
        className={buttonClass}
      >
        <List className={iconClass} />
      </Button>
    </div>
  );
};

export default GridViewToggle;
