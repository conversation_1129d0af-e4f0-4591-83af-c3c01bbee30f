import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { useLanguage } from '@/hooks/useLanguage';
import { BulkAction } from '@/types/admin';
import { ChevronDown, Loader2 } from 'lucide-react';

interface BulkActionsProps {
  selectedIds: string[];
  onAction: (action: BulkAction) => Promise<void>;
  isLoading: boolean;
  onSelectAll: () => void;
  onDeselectAll: () => void;
  totalCount: number;
}

const BulkActions: React.FC<BulkActionsProps> = ({
  selectedIds,
  onAction,
  isLoading,
  onSelectAll,
  onDeselectAll,
  totalCount
}) => {
  const { t } = useLanguage();
  const [confirmAction, setConfirmAction] = useState<BulkAction | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);

  const selectedCount = selectedIds.length;

  const handleAction = async (action: BulkAction) => {
    if (selectedCount === 0) return;
    setConfirmAction(action);
  };

  const confirmAndExecute = async () => {
    if (!confirmAction) return;
    
    setIsProcessing(true);
    try {
      await onAction(confirmAction);
    } finally {
      setIsProcessing(false);
      setConfirmAction(null);
    }
  };

  const getConfirmationMessage = (action: BulkAction) => {
    const key = `admin.confirm${action.charAt(0).toUpperCase() + action.slice(1)}`;
    return t(key).replace('{count}', selectedCount.toString());
  };

  const getActionLabel = (action: BulkAction) => {
    const labelMap: Record<BulkAction, string> = {
      approve: t('admin.approveSelected'),
      reject: t('admin.rejectSelected'),
      delete: t('admin.deleteSelected'),
      block: t('admin.blockSelected'),
      unblock: t('admin.unblockSelected')
    };
    return labelMap[action];
  };

  return (
    <div className="flex items-center gap-4 p-4 bg-muted/50 rounded-lg">
      <div className="flex items-center gap-2">
        <Button
          variant="outline"
          size="sm"
          onClick={selectedCount === totalCount ? onDeselectAll : onSelectAll}
          disabled={isLoading || totalCount === 0}
        >
          {selectedCount === totalCount ? t('admin.deselectAll') : t('admin.selectAll')}
        </Button>
        
        {selectedCount > 0 && (
          <span className="text-sm text-muted-foreground">
            {t('admin.selectedCount').replace('{count}', selectedCount.toString())}
          </span>
        )}
      </div>

      {selectedCount > 0 && (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button 
              variant="default" 
              size="sm" 
              disabled={isLoading || isProcessing}
            >
              {isProcessing ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  {t('admin.processing')}
                </>
              ) : (
                <>
                  {t('admin.bulkActions')}
                  <ChevronDown className="w-4 h-4 ml-2" />
                </>
              )}
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="start">
            <DropdownMenuItem onClick={() => handleAction('approve')}>
              {getActionLabel('approve')}
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => handleAction('reject')}>
              {getActionLabel('reject')}
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => handleAction('block')}>
              {getActionLabel('block')}
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => handleAction('unblock')}>
              {getActionLabel('unblock')}
            </DropdownMenuItem>
            <DropdownMenuItem 
              onClick={() => handleAction('delete')}
              className="text-destructive focus:text-destructive"
            >
              {getActionLabel('delete')}
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      )}

      <AlertDialog open={!!confirmAction} onOpenChange={() => setConfirmAction(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>{t('admin.confirmDelete')}</AlertDialogTitle>
            <AlertDialogDescription>
              {confirmAction && getConfirmationMessage(confirmAction)}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isProcessing}>
              {t('admin.no')}
            </AlertDialogCancel>
            <AlertDialogAction 
              onClick={confirmAndExecute}
              disabled={isProcessing}
              className={confirmAction === 'delete' ? 'bg-destructive hover:bg-destructive/90' : ''}
            >
              {isProcessing ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  {t('admin.processing')}
                </>
              ) : (
                t('admin.yes')
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};

export default BulkActions;
