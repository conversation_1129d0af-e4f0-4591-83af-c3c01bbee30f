
import { createContext, useContext, ReactNode } from 'react';
import { useLanguageProvider } from './useLanguageProvider';

// Define Language type
export type Language = 'en' | 'ru';

type LanguageContextType = {
  language: Language;
  setLanguage: (lang: Language) => void;
  t: (key: string) => string;
};

const LanguageContext = createContext<LanguageContextType | undefined>(undefined);

export const LanguageProvider = ({ children }: { children: ReactNode }) => {
  const languageProviderValues = useLanguageProvider();
  
  return (
    <LanguageContext.Provider value={languageProviderValues}>
      {children}
    </LanguageContext.Provider>
  );
};

export const useLanguage = () => {
  const context = useContext(LanguageContext);
  if (context === undefined) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
};

