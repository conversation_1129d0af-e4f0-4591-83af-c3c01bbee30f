
import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { MessageSquare, Phone, Flag } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import ListingHeader from '@/components/listings/detail/ListingHeader';
import ListingGallery from '@/components/listings/detail/ListingGallery';
import ListingDescription from '@/components/listings/detail/ListingDescription';
import ListingSpecifications from '@/components/listings/detail/ListingSpecifications';
import ListingPriceCard from '@/components/listings/detail/ListingPriceCard';
import SellerCard from '@/components/listings/detail/SellerCard';
import LocationCard from '@/components/listings/detail/LocationCard';
import MessageDialog from '@/components/listings/detail/MessageDialog';
import PhoneDialog from '@/components/listings/detail/PhoneDialog';
import ReportDialog from '@/components/listings/detail/ReportDialog';
import { Listing } from '@/hooks/listings/types';
import { useAuth } from "@/contexts/AuthContext";
import { useLanguage } from "@/hooks/useLanguage";

interface ListingDetailContentProps {
  listing: Listing;
  isOwner: boolean;
  isAuthenticated: boolean;
  sellerPhone: string;
  isSaved: boolean;
  isDeleting: boolean;
  isMobile: boolean;
  onDelete: (id: string) => void;
  onSaveListing: () => void;
}

const ListingDetailContent: React.FC<ListingDetailContentProps> = ({
  listing,
  isOwner,
  isAuthenticated,
  sellerPhone,
  isSaved,
  isDeleting,
  isMobile,
  onDelete,
  onSaveListing
}) => {
  const { user } = useAuth();
  const { t } = useLanguage();
  const navigate = useNavigate();
  const [messageDialogOpen, setMessageDialogOpen] = useState(false);
  const [phoneDialogOpen, setPhoneDialogOpen] = useState(false);
  const [reportDialogOpen, setReportDialogOpen] = useState(false);

  return (
    <>
      <ListingHeader
        title={listing.title}
        make={listing.make}
        model={listing.model}
        year={listing.year}
        isOwner={isOwner}
        listingId={listing.id}
        userId={listing.user_id}
        onDelete={() => onDelete(listing.id)}
        isDeleting={isDeleting}
      />

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <div className="lg:col-span-2 space-y-6">
          <ListingGallery images={listing.image_urls || []} title={listing.title} />

          {/* Contact seller buttons */}
          <div className="mb-6">
            <div className="w-full border rounded-lg p-4 bg-card shadow-sm">
              <h3 className="text-lg font-medium mb-4">{t('listings.contactSeller')}</h3>
              {/* Mobile contact buttons - two thick buttons side by side */}
              {isMobile ? (
                <div className="grid grid-cols-2 gap-3">
                  <Button
                    onClick={() => isAuthenticated ? setMessageDialogOpen(true) : navigate('/auth?returnUrl=' + encodeURIComponent(window.location.pathname))}
                    className="w-full h-14 text-base"
                    size="lg"
                  >
                    <MessageSquare className="mr-2 h-5 w-5" />
                    {t('listings.write')}
                  </Button>

                  <Button
                    onClick={() => isAuthenticated ? setPhoneDialogOpen(true) : navigate('/auth?returnUrl=' + encodeURIComponent(window.location.pathname))}
                    className="w-full h-14 text-base bg-emerald-500 hover:bg-emerald-600"
                    size="lg"
                  >
                    <Phone className="mr-2 h-5 w-5" />
                    {t('listings.call')}
                  </Button>
                </div>
              ) : (
                <div className="flex flex-col sm:flex-row gap-3">
                  <Button
                    onClick={() => isAuthenticated ? setMessageDialogOpen(true) : navigate('/auth?returnUrl=' + encodeURIComponent(window.location.pathname))}
                    className="flex-1"
                    size="lg"
                  >
                    <MessageSquare className="mr-2 h-5 w-5" />
                    {t('listings.write')}
                  </Button>

                  <Button
                    onClick={() => isAuthenticated ? setPhoneDialogOpen(true) : navigate('/auth?returnUrl=' + encodeURIComponent(window.location.pathname))}
                    className="flex-1 bg-emerald-500 hover:bg-emerald-600"
                    size="lg"
                  >
                    <Phone className="mr-2 h-5 w-5" />
                    {t('listings.call')}
                  </Button>
                </div>
              )}
            </div>
          </div>

          <ListingDescription description={listing.description || ''} />
          <ListingSpecifications
            make={listing.make}
            model={listing.model}
            year={listing.year}
            location={listing.location}
          />
        </div>

        <div className="space-y-6">
          <ListingPriceCard
            price={listing.price}
            featured={!!listing.featured}
            isAuthenticated={isAuthenticated}
            isOwner={isOwner}
            isSaved={isSaved}
            onSaveListing={onSaveListing}
            isMobile={isMobile}
            sellerId={listing.user_id}
            listingId={listing.id}
            onReportClick={() => isAuthenticated ? setReportDialogOpen(true) : navigate('/auth?returnUrl=' + encodeURIComponent(window.location.pathname))}
          />

          <SellerCard sellerId={listing.user_id} />
          <LocationCard location={listing.location} />
        </div>
      </div>

      <MessageDialog
        open={messageDialogOpen}
        onOpenChange={setMessageDialogOpen}
        recipientId={listing.user_id}
        senderId={user?.id}
        isAuthenticated={isAuthenticated}
        listingId={listing.id}
      />

      <PhoneDialog
        open={phoneDialogOpen}
        onOpenChange={setPhoneDialogOpen}
        sellerPhone={sellerPhone}
        isAuthenticated={isAuthenticated}
      />

      <ReportDialog
        open={reportDialogOpen}
        onOpenChange={setReportDialogOpen}
        listingId={listing.id}
        userId={user?.id}
        isAuthenticated={isAuthenticated}
      />
    </>
  );
};

export default ListingDetailContent;
