
CREATE OR REPLACE FUNCTION public.submit_report(
  p_user_id UUID, 
  p_listing_id UUID, 
  p_reason TEXT
) RETURNS VOID AS $$
BEGIN
  -- Direct SQL insert since the table might not be present in TypeScript types
  INSERT INTO listing_reports (user_id, listing_id, reason, status) 
  VALUES (p_user_id, p_listing_id, p_reason, 'pending')
  ON CONFLICT (user_id, listing_id) DO NOTHING;
  
  -- Notify admins (if you have admin notification functionality)
  INSERT INTO admin_notifications (type, message, data, read)
  VALUES (
    'listing_report',
    'New listing report submitted',
    jsonb_build_object(
      'listing_id', p_listing_id,
      'user_id', p_user_id,
      'reason', p_reason
    ),
    false
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Add unique constraint to prevent duplicate reports
ALTER TABLE IF EXISTS listing_reports 
ADD CONSTRAINT unique_user_listing_report UNIQUE (user_id, listing_id);

-- Ensure listing_reports table exists
-- (This SQL will only run if the table doesn't already exist from previous migrations)
DO $$ 
BEGIN
  IF NOT EXISTS (SELECT FROM pg_tables WHERE schemaname = 'public' AND tablename = 'listing_reports') THEN
    CREATE TABLE public.listing_reports (
      id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
      user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
      listing_id UUID NOT NULL REFERENCES public.listings(id) ON DELETE CASCADE,
      reason TEXT NOT NULL,
      status TEXT NOT NULL DEFAULT 'pending',
      created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    );
    
    ALTER TABLE public.listing_reports ENABLE ROW LEVEL SECURITY;
    
    -- Add RLS policies
    CREATE POLICY "Users can view their own reports" ON public.listing_reports
      FOR SELECT TO authenticated
      USING (auth.uid() = user_id);
      
    CREATE POLICY "Users can create reports" ON public.listing_reports
      FOR INSERT TO authenticated
      WITH CHECK (auth.uid() = user_id);
      
    -- Add unique constraint
    ALTER TABLE public.listing_reports
      ADD CONSTRAINT unique_user_listing_report UNIQUE (user_id, listing_id);
  END IF;
END $$;
