import { useState, useEffect, useCallback } from 'react';
import { useListings } from '@/hooks/useListings';
import { Listing } from '@/hooks/listings/types';

interface PaginationState {
  currentPage: number;
  itemsPerPage: number;
  totalItems: number;
  totalPages: number;
  listings: Listing[];
  isLoading: boolean;
}

interface PaginationFilters {
  searchTerm?: string;
  featured?: string;
  vipStatus?: string;
  paidStatus?: string;
}

export const useAdminListingsPagination = () => {
  const { fetchPaginatedListings } = useListings();
  
  const [state, setState] = useState<PaginationState>({
    currentPage: 1,
    itemsPerPage: parseInt(localStorage.getItem('adminListingsPerPage') || '50'),
    totalItems: 0,
    totalPages: 0,
    listings: [],
    isLoading: false,
  });
  
  const [filters, setFilters] = useState<PaginationFilters>({
    searchTerm: '',
    featured: 'all',
    vipStatus: 'all',
    paidStatus: 'all',
  });

  // Load listings with current pagination and filters
  const loadListings = useCallback(async () => {
    setState(prev => ({ ...prev, isLoading: true }));
    
    try {
      const result = await fetchPaginatedListings(
        state.currentPage,
        state.itemsPerPage,
        filters.searchTerm,
        {
          featured: filters.featured,
          vipStatus: filters.vipStatus,
          paidStatus: filters.paidStatus,
        }
      );
      
      setState(prev => ({
        ...prev,
        listings: result.listings,
        totalItems: result.totalCount,
        totalPages: result.totalPages,
        isLoading: false,
      }));
    } catch (error) {
      console.error('Error loading paginated listings:', error);
      setState(prev => ({ ...prev, isLoading: false }));
    }
  }, [state.currentPage, state.itemsPerPage, filters, fetchPaginatedListings]);

  // Load listings when dependencies change
  useEffect(() => {
    loadListings();
  }, [loadListings]);

  // Handle page change
  const handlePageChange = useCallback((page: number) => {
    setState(prev => ({ ...prev, currentPage: page }));
    // Scroll to top when changing pages
    window.scrollTo({ top: 0, behavior: 'smooth' });
  }, []);

  // Handle items per page change
  const handleItemsPerPageChange = useCallback((itemsPerPage: number) => {
    // Save to localStorage
    localStorage.setItem('adminListingsPerPage', itemsPerPage.toString());
    
    // Reset to first page and update items per page
    setState(prev => ({
      ...prev,
      currentPage: 1,
      itemsPerPage,
    }));
  }, []);

  // Handle search
  const handleSearch = useCallback((searchTerm: string) => {
    setFilters(prev => ({ ...prev, searchTerm }));
    setState(prev => ({ ...prev, currentPage: 1 })); // Reset to first page
  }, []);

  // Handle filter changes
  const handleFilterChange = useCallback((filterKey: keyof PaginationFilters, value: string) => {
    setFilters(prev => ({ ...prev, [filterKey]: value }));
    setState(prev => ({ ...prev, currentPage: 1 })); // Reset to first page
  }, []);

  // Clear all filters
  const clearFilters = useCallback(() => {
    setFilters({
      searchTerm: '',
      featured: 'all',
      vipStatus: 'all',
      paidStatus: 'all',
    });
    setState(prev => ({ ...prev, currentPage: 1 }));
  }, []);

  // Refresh current page
  const refresh = useCallback(() => {
    loadListings();
  }, [loadListings]);

  return {
    // State
    currentPage: state.currentPage,
    itemsPerPage: state.itemsPerPage,
    totalItems: state.totalItems,
    totalPages: state.totalPages,
    listings: state.listings,
    isLoading: state.isLoading,
    
    // Filters
    searchTerm: filters.searchTerm,
    featuredFilter: filters.featured,
    vipFilter: filters.vipStatus,
    paidFilter: filters.paidStatus,
    
    // Actions
    handlePageChange,
    handleItemsPerPageChange,
    handleSearch,
    handleFilterChange,
    clearFilters,
    refresh,
  };
};
