
import React, { ReactNode } from 'react';
import { useLanguage } from '@/hooks/useLanguage';
import { useAuth } from '@/contexts/AuthContext';
import { Navigate } from 'react-router-dom';
import { toast } from 'sonner';
import { useAdminAccess } from '@/hooks/useAdminAccess';
import { RefreshCcw } from 'lucide-react';

interface AdminAuthProps {
  children: ReactNode;
}

const AdminAuth: React.FC<AdminAuthProps> = ({ children }) => {
  const { t } = useLanguage();
  const { isAuthenticated } = useAuth();
  const { isAdmin, isLoading, error } = useAdminAccess();

  // Отладочная информация
  console.log('AdminAuth - Status:', { isAuthenticated, isAdmin, isLoading, error });

  // If not authenticated, redirect to login
  if (!isAuthenticated && !isLoading) {
    toast.error(t('authRequired'), {
      description: t('adminAuthRequired')
    });
    return <Navigate to="/auth" replace />;
  }

  // If there's an error checking admin status, show error and redirect
  if (error && !isLoading) {
    toast.error(t('accessDenied'), {
      description: t('adminAccessError')
    });
    return <Navigate to="/" replace />;
  }

  // If not an admin, redirect to home
  if (!isAdmin && !isLoading) {
    toast.error(t('accessDenied'), {
      description: t('adminAccessDenied')
    });
    return <Navigate to="/" replace />;
  }

  // Show loading state
  if (isLoading) {
    return (
      <div className="min-h-screen flex flex-col">
        <main className="flex-grow flex items-center justify-center">
          <div className="flex flex-col items-center gap-2">
            <RefreshCcw className="h-8 w-8 animate-spin text-primary" />
            <p className="text-lg">{t('loading')}</p>
          </div>
        </main>
      </div>
    );
  }

  return <>{children}</>;
};

export default AdminAuth;
