
import React from 'react';
import { Link } from 'react-router-dom';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import { Button } from '@/components/ui/button';
import { useLanguage } from '@/hooks/useLanguage';
import BackButton from '@/components/BackButton';

const NotFound = () => {
  const { t } = useLanguage();
  
  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      
      <main className="flex-grow flex items-center justify-center py-12">
        <div className="container px-4 sm:px-6 lg:px-8 text-center">
          <BackButton to="/" className="mx-auto mb-8 justify-center" />
          
          <div className="space-y-6">
            <h1 className="text-4xl font-bold">404</h1>
            <h2 className="text-2xl font-semibold">{t('pageNotFound')}</h2>
            <p className="text-muted-foreground max-w-md mx-auto">
              {t('pageNotFoundDescription')}
            </p>
            
            <div className="pt-4">
              <Button asChild>
                <Link to="/">{t('backToHome')}</Link>
              </Button>
            </div>
          </div>
        </div>
      </main>
      
      <Footer />
    </div>
  );
};

export default NotFound;
