import React from 'react';
import { useLanguage } from '@/hooks/useLanguage';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';

interface PendingListingsToggleProps {
  autoApprove: boolean;
  isAutoApproveToggling: boolean;
  onAutoApproveToggle: () => Promise<void>;
}

const PendingListingsToggle: React.FC<PendingListingsToggleProps> = ({
  autoApprove,
  isAutoApproveToggling,
  onAutoApproveToggle
}) => {
  const { t } = useLanguage();
  
  return (
    <div className="flex items-center space-x-2">
      <Switch
        id="autoApprove"
        checked={autoApprove}
        onCheckedChange={onAutoApproveToggle}
        disabled={isAutoApproveToggling}
      />
      <Label htmlFor="autoApprove" className="cursor-pointer text-sm">
        {t('admin.autoApprove')}
      </Label>
    </div>
  );
};

export default PendingListingsToggle;
