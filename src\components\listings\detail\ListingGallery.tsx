
import React, { useState } from 'react';
import { useLanguage } from '@/hooks/useLanguage';
import { Carousel, CarouselContent, CarouselItem, CarouselNext, CarouselPrevious } from '@/components/ui/carousel';
import { AspectRatio } from '@/components/ui/aspect-ratio';
import { Dialog, DialogContent, DialogClose } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { ChevronLeft, ChevronRight, X, Maximize2 } from 'lucide-react';
import useEmblaCarousel from 'embla-carousel-react';

interface ListingGalleryProps {
  images: string[];
  title: string;
}

const ListingGallery: React.FC<ListingGalleryProps> = ({ images, title }) => {
  const { t } = useLanguage();
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [lightboxOpen, setLightboxOpen] = useState(false);
  const [lightboxIndex, setLightboxIndex] = useState(0);

  const hasImages = images && images.length > 0;
  const [emblaRef, emblaApi] = useEmblaCarousel();

  // Update current slide index when the carousel scrolls
  React.useEffect(() => {
    if (!emblaApi) return;

    const onSelect = () => {
      setCurrentImageIndex(emblaApi.selectedScrollSnap());
    };

    emblaApi.on('select', onSelect);
    // Initial call to set the index correctly
    onSelect();

    return () => {
      emblaApi.off('select', onSelect);
    };
  }, [emblaApi]);

  const openLightbox = (index: number) => {
    setLightboxIndex(index);
    setLightboxOpen(true);
  };

  const nextLightboxImage = () => {
    setLightboxIndex((prev) => (prev + 1) % images.length);
  };

  const prevLightboxImage = () => {
    setLightboxIndex((prev) => (prev - 1 + images.length) % images.length);
  };

  return (
    <>
      <div className="rounded-lg overflow-hidden bg-muted relative">
        {hasImages ? (
          <>
            <Carousel className="w-full">
              <CarouselContent ref={emblaRef}>
                {images.map((image, index) => (
                  <CarouselItem key={index}>
                    <AspectRatio ratio={16 / 9}>
                      <div className="relative w-full h-full">
                        <img
                          src={image}
                          alt={`${title} - image ${index + 1}`}
                          className="w-full h-full object-cover"
                        />
                        <Button
                          size="icon"
                          variant="secondary"
                          className="absolute top-2 right-2 opacity-80 hover:opacity-100"
                          onClick={() => openLightbox(index)}
                        >
                          <Maximize2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </AspectRatio>
                  </CarouselItem>
                ))}
              </CarouselContent>
              <div className="absolute bottom-4 left-0 right-0 flex justify-center">
                <div className="flex gap-1.5 px-2 py-1 bg-background/70 backdrop-blur-sm rounded-full">
                  {images.map((_, index) => (
                    <div
                      key={index}
                      className={`h-1.5 rounded-full transition-all duration-300 ${
                        currentImageIndex === index ? "bg-primary w-4" : "bg-muted-foreground/40 w-1.5"
                      }`}
                    />
                  ))}
                </div>
              </div>
              <CarouselPrevious className="left-2" />
              <CarouselNext className="right-2" />
            </Carousel>
          </>
        ) : (
          <AspectRatio ratio={16 / 9}>
            <div className="w-full h-full flex items-center justify-center bg-secondary">
              <img
                src="/car-silhouette.svg"
                alt={t('listings.noImageAvailable')}
                className="w-1/3 opacity-30"
              />
            </div>
          </AspectRatio>
        )}
      </div>

      {/* Lightbox */}
      <Dialog open={lightboxOpen} onOpenChange={setLightboxOpen}>
        <DialogContent className="max-w-screen-lg w-[90vw] p-0 bg-background/95 backdrop-blur-md">
          <DialogClose className="absolute right-4 top-4 z-50 rounded-full bg-background/80 p-1">
            <X className="h-6 w-6" />
          </DialogClose>

          {hasImages && (
            <div className="relative h-[80vh] w-full">
              <img
                src={images[lightboxIndex]}
                alt={`${title} - fullscreen image ${lightboxIndex + 1}`}
                className="w-full h-full object-contain"
              />

              <Button
                variant="outline"
                size="icon"
                className="absolute left-4 top-1/2 -translate-y-1/2 bg-background/80 rounded-full"
                onClick={prevLightboxImage}
              >
                <ChevronLeft className="h-8 w-8" />
              </Button>

              <Button
                variant="outline"
                size="icon"
                className="absolute right-4 top-1/2 -translate-y-1/2 bg-background/80 rounded-full"
                onClick={nextLightboxImage}
              >
                <ChevronRight className="h-8 w-8" />
              </Button>

              <div className="absolute bottom-4 left-0 right-0 flex justify-center">
                <div className="flex gap-2 px-3 py-1.5 bg-background/70 rounded-full">
                  {images.map((_, index) => (
                    <div
                      key={index}
                      className={`h-2 rounded-full transition-all duration-300 ${
                        lightboxIndex === index ? "bg-primary w-6" : "bg-muted-foreground/40 w-2"
                      }`}
                      onClick={() => setLightboxIndex(index)}
                    />
                  ))}
                </div>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </>
  );
};

export default ListingGallery;
