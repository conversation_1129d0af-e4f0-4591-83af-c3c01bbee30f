import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import { useLanguage } from '@/hooks/useLanguage';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Mail } from 'lucide-react';
import { toast } from 'sonner';
import { supabase } from '@/integrations/supabase/client';
import BackButton from '@/components/BackButton';

const ForgotPassword: React.FC = () => {
  const { t } = useLanguage();
  const [email, setEmail] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!email) {
      toast.error("Error", {
        description: "Please enter your email address"
      });
      return;
    }

    try {
      setIsLoading(true);
      
      const { error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: `${window.location.origin}/reset-password`,
      });
      
      if (error) {
        console.error('Password reset error:', error);
        toast.error("Password reset error", {
          description: error.message
        });
        throw error;
      }
      
      setIsSuccess(true);
      toast.success("Reset link sent", {
        description: "Check your email for the password reset link"
      });
    } catch (error) {
      console.error('Error resetting password:', error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      
      <main className="flex-grow pt-20">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-10">
          <div className="max-w-md mx-auto">
            <BackButton to="/auth" />
            
            <div className="bg-card border rounded-xl p-6 shadow-subtle">
              <div className="text-center mb-6">
                <h1 className="text-2xl font-bold">{t('forgotPassword')}</h1>
                <p className="text-muted-foreground mt-2">
                  {isSuccess 
                    ? t('checkYourEmail') 
                    : t('enterEmailToReset')}
                </p>
              </div>
              
              {!isSuccess ? (
                <form onSubmit={handleSubmit} className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="email">{t('email')}</Label>
                    <div className="relative">
                      <Input 
                        id="email" 
                        type="email" 
                        placeholder="<EMAIL>" 
                        value={email}
                        onChange={(e) => setEmail(e.target.value)}
                        className="pl-10" 
                      />
                      <Mail className="absolute left-3 top-2.5 h-5 w-5 text-muted-foreground" />
                    </div>
                  </div>
                  
                  <Button className="w-full" type="submit" disabled={isLoading}>
                    {isLoading ? t('sending') : t('resetPassword')}
                  </Button>
                </form>
              ) : (
                <div className="text-center p-4">
                  <div className="mb-4 text-primary">
                    <Mail className="h-12 w-12 mx-auto" />
                  </div>
                  <p className="mb-4">{t('resetLinkSent')}</p>
                  <Button variant="outline" asChild>
                    <Link to="/auth">{t('backToLogin')}</Link>
                  </Button>
                </div>
              )}
              
              <div className="text-center text-sm text-muted-foreground mt-6">
                <span>{t('rememberPassword')} </span>
                <Link to="/auth" className="text-primary hover:underline">
                  {t('signIn')}
                </Link>
              </div>
            </div>
          </div>
        </div>
      </main>
      
      <Footer />
    </div>
  );
};

export default ForgotPassword;
