
import React, { useEffect, useState } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { useLanguage } from '@/hooks/useLanguage';
import { supabase } from '@/integrations/supabase/client';

interface SellerCardProps {
  sellerId: string;
}

type SellerProfile = {
  id: string;
  username: string | null;
  full_name: string | null;
  avatar_url: string | null;
  created_at: string;
}

const SellerCard: React.FC<SellerCardProps> = ({ sellerId }) => {
  const { t } = useLanguage();
  const [sellerProfile, setSellerProfile] = useState<SellerProfile | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Get translated text first to ensure proper rendering
  const sellerInfoTitle = t('listings.sellerInformation');
  const sellerLabel = t('listings.seller');
  const memberSinceLabel = t('listings.memberSince');
  const responseRateLabel = t('listings.responseRate');
  const avgResponseTimeLabel = t('listings.averageResponseTime');
  const hoursLabel = t('listings.hours');

  useEffect(() => {
    const fetchSellerProfile = async () => {
      if (!sellerId) return;

      try {
        setIsLoading(true);
        const { data, error } = await supabase
          .from('profiles')
          .select('*')
          .eq('id', sellerId)
          .single();

        if (error) throw error;

        setSellerProfile(data);
      } catch (error) {
        console.error('Error fetching seller profile:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchSellerProfile();
  }, [sellerId]);

  // Calculate member since date
  const memberSince = sellerProfile?.created_at
    ? new Date(sellerProfile.created_at).getFullYear()
    : new Date().getFullYear();

  // Get seller display name (full name, username, or fallback)
  const sellerName = sellerProfile?.full_name ||
    sellerProfile?.username ||
    sellerLabel;

  // Get initials for avatar fallback
  const getInitials = () => {
    if (sellerProfile?.full_name) {
      return sellerProfile.full_name
        .split(' ')
        .map(name => name[0])
        .join('')
        .toUpperCase()
        .substring(0, 2);
    }

    if (sellerProfile?.username) {
      return sellerProfile.username.substring(0, 2).toUpperCase();
    }

    return 'S';
  };

  return (
    <Card>
      <CardHeader className="pb-3">
        <CardTitle>{sellerInfoTitle}</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="flex items-center space-x-4 mb-4">
          <Avatar className="h-10 w-10">
            {sellerProfile?.avatar_url ? (
              <AvatarImage src={sellerProfile.avatar_url} alt={sellerName} />
            ) : null}
            <AvatarFallback>{getInitials()}</AvatarFallback>
          </Avatar>
          <div>
            <div className="font-medium">{sellerName}</div>
            <div className="text-xs text-muted-foreground">
              {memberSinceLabel} {memberSince}
            </div>
          </div>
        </div>

        <div className="grid grid-cols-2 gap-2 text-sm">
          <div>
            <div className="text-muted-foreground">{responseRateLabel}</div>
            <div className="font-medium">95%</div>
          </div>
          <div>
            <div className="text-muted-foreground">{avgResponseTimeLabel}</div>
            <div className="font-medium">2 {hoursLabel}</div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default SellerCard;
