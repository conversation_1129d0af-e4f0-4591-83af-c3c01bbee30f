
import React from 'react';
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from '@/components/ui/pagination';
import { Button } from '@/components/ui/button';
import { ChevronLeft, ChevronRight, ChevronsLeft, ChevronsRight } from 'lucide-react';
import { useLanguage } from '@/hooks/useLanguage';

interface AdminListingsPaginationProps {
  currentPage: number;
  totalPages: number;
  totalItems: number;
  itemsPerPage: number;
  onPageChange: (page: number) => void;
  isLoading?: boolean;
}

const AdminListingsPagination: React.FC<AdminListingsPaginationProps> = ({
  currentPage,
  totalPages,
  totalItems,
  itemsPerPage,
  onPageChange,
  isLoading = false,
}) => {
  const { t } = useLanguage();

  // Don't render pagination if there are no items
  if (totalItems === 0) return null;

  // Calculate display range
  const startItem = (currentPage - 1) * itemsPerPage + 1;
  const endItem = Math.min(currentPage * itemsPerPage, totalItems);

  // Generate pagination numbers efficiently
  const getPaginationRange = () => {
    const delta = 1; // How many pages to show before and after current page
    let range = [];

    for (let i = Math.max(2, currentPage - delta);
         i <= Math.min(totalPages - 1, currentPage + delta);
         i++) {
      range.push(i);
    }

    // Add first page if not already included
    if (range.length === 0 || range[0] !== 1) {
      range.unshift(1);
    }

    // Add last page if not already included and if totalPages > 1
    if (totalPages > 1 && range[range.length - 1] !== totalPages) {
      range.push(totalPages);
    }

    return range;
  };

  const paginationRange = getPaginationRange();

  return (
    <div className="flex flex-col sm:flex-row items-center justify-between gap-4 mt-6">
      {/* Items info */}
      <div className="text-sm text-muted-foreground">
        {t('admin.showing')} {startItem}-{endItem} {t('admin.of')} {totalItems} {t('admin.items')}
      </div>

      {/* Pagination controls */}
      <div className="flex items-center gap-2">
        {/* First page button */}
        <Button
          variant="outline"
          size="sm"
          onClick={() => onPageChange(1)}
          disabled={currentPage === 1 || isLoading}
          className="hidden sm:flex"
        >
          <ChevronsLeft className="h-4 w-4" />
        </Button>

        {/* Previous page button */}
        <Button
          variant="outline"
          size="sm"
          onClick={() => onPageChange(currentPage - 1)}
          disabled={currentPage === 1 || isLoading}
        >
          <ChevronLeft className="h-4 w-4" />
          <span className="hidden sm:inline ml-1">{t('ui.back')}</span>
        </Button>

        {/* Page numbers */}
        <Pagination className="mx-0">
          <PaginationContent>
            {paginationRange.map((page, i, array) => {
              const items = [];

              // Add the page number
              items.push(
                <PaginationItem key={`page-${page}`}>
                  <PaginationLink
                    isActive={page === currentPage}
                    onClick={() => onPageChange(page)}
                    className="cursor-pointer"
                    disabled={isLoading}
                  >
                    {page}
                  </PaginationLink>
                </PaginationItem>
              );

              // Add ellipsis if there's a gap
              if (i < array.length - 1 && array[i + 1] - page > 1) {
                items.push(
                  <PaginationItem key={`ellipsis-${page}`}>
                    <PaginationEllipsis />
                  </PaginationItem>
                );
              }

              return items;
            })}
          </PaginationContent>
        </Pagination>

        {/* Next page button */}
        <Button
          variant="outline"
          size="sm"
          onClick={() => onPageChange(currentPage + 1)}
          disabled={currentPage === totalPages || isLoading}
        >
          <span className="hidden sm:inline mr-1">{t('ui.next')}</span>
          <ChevronRight className="h-4 w-4" />
        </Button>

        {/* Last page button */}
        <Button
          variant="outline"
          size="sm"
          onClick={() => onPageChange(totalPages)}
          disabled={currentPage === totalPages || isLoading}
          className="hidden sm:flex"
        >
          <ChevronsRight className="h-4 w-4" />
        </Button>
      </div>

      {/* Page info */}
      <div className="text-sm text-muted-foreground">
        {t('listings.page')} {currentPage} {t('listings.of')} {totalPages}
      </div>
    </div>
  );
};

export default AdminListingsPagination;
