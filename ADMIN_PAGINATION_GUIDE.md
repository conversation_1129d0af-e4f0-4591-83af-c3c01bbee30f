# Руководство по пагинации в административной панели

## ✅ Реализованные функции

### **1. Серверная пагинация**
- **По умолчанию**: 50 объявлений на странице
- **Оптимизация**: Загружаются только нужные данные с сервера
- **Производительность**: Быстрая работа даже с большим количеством объявлений

### **2. Настройка количества элементов**
- **Опции**: 25, 50, 100, 200 объявлений на странице
- **Сохранение**: Выбор сохраняется в localStorage
- **Автообновление**: Мгновенное обновление при изменении

### **3. Навигация по страницам**
- **Кнопки**: Первая, Предыдущая, Следующая, Последняя страница
- **Номера страниц**: Умная система отображения с многоточием
- **Быстрый переход**: Клик по номеру страницы для мгновенного перехода

### **4. Информация о данных**
- **Диапазон**: "Показано 1-50 из 411 элементов"
- **Страницы**: "Страница 1 из 9"
- **Общее количество**: Отображение общего числа объявлений

### **5. Интеграция с фильтрами**
- **Сохранение страницы**: При фильтрации сбрасывается на первую страницу
- **Поиск**: Работает с серверной пагинацией
- **Фильтры**: Featured, VIP, Paid статусы работают с пагинацией

### **6. Индикаторы загрузки**
- **Состояние загрузки**: Показывается при переключении страниц
- **Отключение кнопок**: Кнопки неактивны во время загрузки
- **Плавные переходы**: Автоматическая прокрутка к началу страницы

### **7. Responsive дизайн**
- **Мобильные устройства**: Адаптивная компоновка
- **Скрытие элементов**: Некоторые кнопки скрываются на маленьких экранах
- **Гибкая верстка**: Элементы перестраиваются под размер экрана

## 📁 Измененные файлы

### **Новые файлы:**
1. `src/hooks/useAdminListingsPagination.ts` - Хук для управления пагинацией
2. `src/components/admin/listings/ItemsPerPageSelector.tsx` - Селектор количества элементов

### **Обновленные файлы:**
1. `src/hooks/useListings.ts` - Добавлена функция `fetchPaginatedListings`
2. `src/components/admin/AdminListings.tsx` - Полностью переработан для серверной пагинации
3. `src/components/admin/listings/AdminListingsPagination.tsx` - Улучшенный компонент пагинации
4. `src/utils/i18n/translations/ru/admin.ts` - Добавлены переводы для пагинации

## 🎯 Технические особенности

### **Серверная пагинация:**
```typescript
// Запрос с offset и limit
const offset = (page - 1) * limit;
const query = supabase
  .from('listings')
  .select('*', { count: 'exact' })
  .range(offset, offset + limit - 1);
```

### **Сохранение настроек:**
```typescript
// Сохранение в localStorage
localStorage.setItem('adminListingsPerPage', itemsPerPage.toString());
```

### **Фильтрация и поиск:**
```typescript
// Поиск по нескольким полям
query = query.or(`title.ilike.%${searchLower}%,make.ilike.%${searchLower}%`);
```

## 🚀 Использование

### **Переключение страниц:**
1. Используйте кнопки "◀◀", "◀", "▶", "▶▶" для навигации
2. Кликайте по номерам страниц для прямого перехода
3. Автоматическая прокрутка к началу страницы

### **Изменение количества элементов:**
1. Выберите нужное количество в селекторе справа
2. Настройка автоматически сохранится
3. Страница обновится с новым количеством элементов

### **Работа с фильтрами:**
1. Поиск и фильтры работают с пагинацией
2. При изменении фильтров происходит сброс на первую страницу
3. Общее количество элементов обновляется в реальном времени

## 📱 Мобильная версия

- **Адаптивная навигация**: Кнопки перестраиваются под размер экрана
- **Скрытие элементов**: Кнопки "Первая" и "Последняя" скрываются на маленьких экранах
- **Компактный дизайн**: Оптимизированное отображение информации

## 🔧 Настройки по умолчанию

- **Элементов на странице**: 50
- **Доступные опции**: 25, 50, 100, 200
- **Сортировка**: По дате создания (новые сначала)
- **Автосохранение**: Настройки сохраняются автоматически
