
import React from 'react';
import Header from '@/components/Header';
import Footer from '@/components/Footer';

interface ListingDetailLoadingProps {
  isMobile: boolean;
}

const ListingDetailLoading: React.FC<ListingDetailLoadingProps> = ({ isMobile }) => {
  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      <main className={`flex-grow container mx-auto px-4 py-8 ${isMobile ? 'pt-20' : 'pt-24'}`}>
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
        </div>
      </main>
      <Footer />
    </div>
  );
};

export default ListingDetailLoading;
