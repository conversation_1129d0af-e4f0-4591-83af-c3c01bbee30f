
import React from 'react';
import { Menu, X, MessageSquare } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { useLanguage } from '@/hooks/useLanguage';
import { Badge } from '@/components/ui/badge';
import { useMessages } from '@/hooks/useMessages';
import AuthButtons from './AuthButtons';
import ThemeToggle from './ThemeToggle';
import LanguageSelector from './LanguageSelector';

interface MobileActionsProps {
  mobileMenuOpen: boolean;
  toggleMobileMenu: () => void;
}

const MobileActions: React.FC<MobileActionsProps> = ({ 
  mobileMenuOpen, 
  toggleMobileMenu 
}) => {
  const navigate = useNavigate();
  const { isAuthenticated } = useAuth();
  const { t } = useLanguage();
  const { unreadCount } = useMessages();

  return (
    <div className="flex md:hidden items-center gap-2">
      {!isAuthenticated && (
        <div className="flex items-center mr-2">
          <AuthButtons />
        </div>
      )}
      
      <div className="flex items-center gap-2">
        <ThemeToggle />
        <LanguageSelector />
        {isAuthenticated && (
          <Button 
            variant="ghost" 
            size="icon" 
            onClick={() => navigate('/messages')}
            aria-label={t('messages')}
            title={t('messages')}
            className="relative"
          >
            <MessageSquare className="h-5 w-5" />
            {unreadCount > 0 && (
              <Badge variant="destructive" className="absolute -top-1 -right-1 h-5 w-5 flex items-center justify-center p-0 text-xs">
                {unreadCount}
              </Badge>
            )}
          </Button>
        )}
        
        <Button 
          variant="ghost" 
          size="icon" 
          onClick={toggleMobileMenu}
          aria-label={mobileMenuOpen ? "Close menu" : "Open menu"}
        >
          {mobileMenuOpen ? (
            <X className="h-5 w-5" />
          ) : (
            <Menu className="h-5 w-5" />
          )}
        </Button>
      </div>
    </div>
  );
};

export default MobileActions;
