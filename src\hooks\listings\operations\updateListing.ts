
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';
import { Listing } from '../types';

export const useUpdateListing = (
  setIsLoading: React.Dispatch<React.SetStateAction<boolean>>,
  setListings: React.Dispatch<React.SetStateAction<Listing[]>>,
  setFeaturedListings: React.Dispatch<React.SetStateAction<Listing[]>>
) => {
  const updateListing = async (id: string, listing: Partial<Listing>) => {
    try {
      setIsLoading(true);

      const { data, error } = await supabase
        .from('listings')
        .update(listing)
        .eq('id', id)
        .select()
        .single();

      if (error) {
        throw error;
      }

      const updatedListing = data as Listing;

      setListings(prevListings =>
        prevListings.map(item => item.id === id ? updatedListing : item)
      );

      setFeaturedListings(prevFeatured => {
        const idx = prevFeatured.findIndex(item => item.id === id);
        if (idx >= 0) {
          const newFeatured = [...prevFeatured];
          newFeatured[idx] = updatedListing;
          return newFeatured;
        }
        return prevFeatured;
      });

      // Success message is handled by the frontend form submission
      return updatedListing;
    } catch (error) {
      console.error('Error updating listing:', error);
      toast.error("Ошибка при обновлении объявления", {
        description: "Не удалось обновить объявление. Пожалуйста, попробуйте еще раз."
      });
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  return { updateListing };
};
