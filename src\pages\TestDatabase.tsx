import React, { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { Button } from '@/components/ui/button';
import { createTestData, checkDatabaseConnection } from '@/utils/createTestData';

const TestDatabase: React.FC = () => {
  const [result, setResult] = useState<string>('');
  const [isLoading, setIsLoading] = useState(false);
  const [listings, setListings] = useState<any[]>([]);

  useEffect(() => {
    // Auto-check database on load
    handleCheckDatabase();
  }, []);

  const handleCheckDatabase = async () => {
    setIsLoading(true);
    setResult('Checking database connection...');
    
    try {
      const dbResult = await checkDatabaseConnection();
      if (dbResult.success) {
        setResult(`✅ Database connection successful! Found ${dbResult.count} listings`);
        setListings(dbResult.data || []);
      } else {
        setResult(`❌ Database connection failed: ${dbResult.error}`);
      }
    } catch (error) {
      setResult(`❌ Error: ${error.message}`);
    } finally {
      setIsLoading(false);
    }
  };

  const handleCreateTestData = async () => {
    setIsLoading(true);
    setResult('Creating test data...');
    
    try {
      const createResult = await createTestData();
      if (createResult.success) {
        setResult(`✅ Test data created! Inserted ${createResult.inserted} listings. Total: ${createResult.total}`);
        // Refresh listings
        await handleCheckDatabase();
      } else {
        setResult(`❌ Failed to create test data: ${createResult.error}`);
      }
    } catch (error) {
      setResult(`❌ Error: ${error.message}`);
    } finally {
      setIsLoading(false);
    }
  };

  const handleTestPaginatedQuery = async () => {
    setIsLoading(true);
    setResult('Testing paginated query...');
    
    try {
      const offset = 0;
      const limit = 10;
      
      const { data, error, count } = await supabase
        .from('listings')
        .select('*', { count: 'exact' })
        .order('created_at', { ascending: false })
        .range(offset, offset + limit - 1);
      
      if (error) {
        throw error;
      }
      
      setResult(`✅ Paginated query successful! Found ${count} total listings, showing ${data.length}`);
      setListings(data || []);
    } catch (error) {
      setResult(`❌ Paginated query failed: ${error.message}`);
    } finally {
      setIsLoading(false);
    }
  };

  const handleClearDatabase = async () => {
    if (!confirm('Are you sure you want to delete ALL listings? This cannot be undone!')) {
      return;
    }
    
    setIsLoading(true);
    setResult('Clearing database...');
    
    try {
      const { error } = await supabase
        .from('listings')
        .delete()
        .neq('id', '00000000-0000-0000-0000-000000000000'); // Delete all except non-existent ID
      
      if (error) {
        throw error;
      }
      
      setResult('✅ Database cleared successfully!');
      setListings([]);
    } catch (error) {
      setResult(`❌ Failed to clear database: ${error.message}`);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div style={{ padding: '20px', fontFamily: 'Arial, sans-serif' }}>
      <h1 style={{ color: '#333', marginBottom: '20px' }}>Database Test Page</h1>
      
      <div style={{ marginBottom: '20px' }}>
        <div style={{ display: 'flex', gap: '10px', marginBottom: '20px' }}>
          <Button onClick={handleCheckDatabase} disabled={isLoading}>
            Check Database
          </Button>
          <Button onClick={handleCreateTestData} disabled={isLoading}>
            Create Test Data
          </Button>
          <Button onClick={handleTestPaginatedQuery} disabled={isLoading}>
            Test Paginated Query
          </Button>
          <Button onClick={handleClearDatabase} disabled={isLoading} variant="destructive">
            Clear Database
          </Button>
        </div>
        
        <div style={{
          padding: '15px',
          backgroundColor: result.includes('✅') ? '#d4edda' : result.includes('❌') ? '#f8d7da' : '#e2e3e5',
          border: `1px solid ${result.includes('✅') ? '#c3e6cb' : result.includes('❌') ? '#f5c6cb' : '#d6d8db'}`,
          borderRadius: '5px',
          color: result.includes('✅') ? '#155724' : result.includes('❌') ? '#721c24' : '#383d41',
          minHeight: '50px'
        }}>
          {result || 'Click a button to test database functionality'}
        </div>
      </div>
      
      <div>
        <h2 style={{ color: '#333', marginBottom: '15px' }}>
          Listings in Database ({listings.length})
        </h2>
        
        {listings.length === 0 ? (
          <p style={{ color: '#666', fontStyle: 'italic' }}>No listings found</p>
        ) : (
          <table style={{ 
            width: '100%', 
            borderCollapse: 'collapse',
            border: '1px solid #ddd'
          }}>
            <thead>
              <tr style={{ backgroundColor: '#f8f9fa' }}>
                <th style={{ border: '1px solid #ddd', padding: '12px', textAlign: 'left' }}>ID</th>
                <th style={{ border: '1px solid #ddd', padding: '12px', textAlign: 'left' }}>Title</th>
                <th style={{ border: '1px solid #ddd', padding: '12px', textAlign: 'left' }}>Make</th>
                <th style={{ border: '1px solid #ddd', padding: '12px', textAlign: 'left' }}>Model</th>
                <th style={{ border: '1px solid #ddd', padding: '12px', textAlign: 'left' }}>Price</th>
                <th style={{ border: '1px solid #ddd', padding: '12px', textAlign: 'left' }}>Status</th>
                <th style={{ border: '1px solid #ddd', padding: '12px', textAlign: 'left' }}>Created</th>
              </tr>
            </thead>
            <tbody>
              {listings.map((listing, index) => (
                <tr key={listing.id} style={{ 
                  backgroundColor: index % 2 === 0 ? '#ffffff' : '#f8f9fa' 
                }}>
                  <td style={{ border: '1px solid #ddd', padding: '8px', fontSize: '12px' }}>
                    {listing.id.substring(0, 8)}...
                  </td>
                  <td style={{ border: '1px solid #ddd', padding: '8px' }}>
                    {listing.title}
                  </td>
                  <td style={{ border: '1px solid #ddd', padding: '8px' }}>
                    {listing.make}
                  </td>
                  <td style={{ border: '1px solid #ddd', padding: '8px' }}>
                    {listing.model}
                  </td>
                  <td style={{ border: '1px solid #ddd', padding: '8px' }}>
                    ${listing.price?.toLocaleString()}
                  </td>
                  <td style={{ border: '1px solid #ddd', padding: '8px' }}>
                    <span style={{
                      padding: '2px 6px',
                      borderRadius: '3px',
                      fontSize: '12px',
                      backgroundColor: listing.status === 'approved' ? '#d4edda' : 
                                     listing.status === 'pending' ? '#fff3cd' : '#f8d7da',
                      color: listing.status === 'approved' ? '#155724' : 
                             listing.status === 'pending' ? '#856404' : '#721c24'
                    }}>
                      {listing.status || 'unknown'}
                    </span>
                  </td>
                  <td style={{ border: '1px solid #ddd', padding: '8px', fontSize: '12px' }}>
                    {new Date(listing.created_at).toLocaleDateString()}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        )}
      </div>
      
      <div style={{ marginTop: '30px', padding: '15px', backgroundColor: '#f8f9fa', borderRadius: '5px' }}>
        <h3 style={{ color: '#333', marginBottom: '10px' }}>Instructions:</h3>
        <ol style={{ color: '#666', lineHeight: '1.6' }}>
          <li><strong>Check Database:</strong> Tests connection and shows existing listings</li>
          <li><strong>Create Test Data:</strong> Adds sample listings with different statuses</li>
          <li><strong>Test Paginated Query:</strong> Tests the same query used by admin panel</li>
          <li><strong>Clear Database:</strong> Removes all listings (use with caution!)</li>
        </ol>
        <p style={{ color: '#666', marginTop: '10px', fontSize: '14px' }}>
          <strong>Note:</strong> This page helps diagnose database issues. 
          If listings show here but not in admin panel, the problem is in the React components.
        </p>
      </div>
    </div>
  );
};

export default TestDatabase;
