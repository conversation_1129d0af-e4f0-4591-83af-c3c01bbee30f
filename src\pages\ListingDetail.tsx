
import React, { useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { useIsMobile } from '@/hooks/use-mobile';
import { useListingDetail } from '@/hooks/listings/useListingDetail';
import ListingDetailLayout from '@/components/listings/detail/ListingDetailLayout';
import ListingDetailLoading from '@/components/listings/detail/ListingDetailLoading';
import ListingNotFound from '@/components/listings/detail/ListingNotFound';
import ListingDetailContent from '@/components/listings/detail/ListingDetailContent';

const ListingDetail = () => {
  const { id } = useParams<{ id: string }>();
  const isMobile = useIsMobile();
  const {
    listing,
    isLoading,
    isDeleting,
    isOwner,
    isAuthenticated,
    sellerPhone,
    isSaved,
    handleSaveListing,
    handleDelete
  } = useListingDetail(id);

  // Подробный отладочный лог для страницы деталей объявления
  useEffect(() => {
    console.log("ListingDetail page rendered with:", {
      authenticated: isAuthenticated,
      isOwner,
      sellerPhone,
      isSaved,
      listingId: id,
      hasListing: !!listing
    });
  }, [isAuthenticated, isOwner, sellerPhone, isSaved, id, listing]);

  if (isLoading) {
    return <ListingDetailLoading isMobile={isMobile} />;
  }

  if (!listing) {
    return <ListingNotFound isMobile={isMobile} />;
  }

  return (
    <ListingDetailLayout isMobile={isMobile}>
      <ListingDetailContent
        listing={listing}
        isOwner={isOwner}
        isAuthenticated={isAuthenticated} // Используем реальное значение аутентификации
        sellerPhone={sellerPhone}
        isSaved={isSaved}
        isDeleting={isDeleting}
        isMobile={isMobile}
        onDelete={handleDelete}
        onSaveListing={handleSaveListing}
      />
    </ListingDetailLayout>
  );
};

export default ListingDetail;
