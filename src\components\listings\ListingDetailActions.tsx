import React, { useState } from 'react';
import { Button } from "@/components/ui/button";
import { MessageSquare, Phone, Heart } from "lucide-react";
import { useAuth } from "@/contexts/AuthContext";
import { useLanguage } from "@/hooks/useLanguage";
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog";
import { Textarea } from "@/components/ui/textarea";
import { toast } from "sonner";
import { supabase } from "@/integrations/supabase/client";

interface ListingDetailActionsProps {
  listingId: string;
  sellerId: string;
  onSaveListing?: () => void;
  isSaved?: boolean;
  onDelete?: () => void;
  isDeleting?: boolean;
  sellerPhone?: string;
}

const ListingDetailActions: React.FC<ListingDetailActionsProps> = ({
  listingId,
  sellerId,
  onSaveListing,
  isSaved,
  onDelete,
  isDeleting,
  sellerPhone = "+123456789" // Default fallback
}) => {
  const { t } = useLanguage();
  const { isAuthenticated, user } = useAuth();
  const [messageDialogOpen, setMessageDialogOpen] = useState(false);
  const [message, setMessage] = useState('');
  const [isSending, setIsSending] = useState(false);
  const [phoneDialogOpen, setPhoneDialogOpen] = useState(false);

  // Проверим переводы
  console.log("Translation test in ListingDetailActions:");
  console.log("listings.sendMessage:", t('listings.sendMessage'));
  console.log("listings.saved:", t('listings.saved'));
  console.log("listings.save:", t('listings.save'));
  console.log("listings.call:", t('listings.call'));
  console.log("listings.deleting:", t('listings.deleting'));
  console.log("listings.deleteListing:", t('listings.deleteListing'));
  console.log("listings.sellerContact:", t('listings.sellerContact'));
  console.log("listings.contactSellerByPhone:", t('listings.contactSellerByPhone'));
  console.log("listings.callNow:", t('listings.callNow'));

  const isOwner = user && user.id === sellerId;

  const handleSendMessage = async () => {
    if (!message.trim() || !isAuthenticated || !user) return;

    try {
      setIsSending(true);

      const messageData = {
        sender_id: user.id,
        recipient_id: sellerId,
        content: message.trim(),
        read: false,
        listing_id: listingId || null
      };

      const { error } = await supabase
        .from('messages')
        .insert(messageData);

      if (error) throw error;

      toast.success(t('listings.messageSent'));
      setMessage('');
      setMessageDialogOpen(false);
    } catch (error) {
      console.error('Error sending message:', error);
      toast.error(t('listings.errorSendingMessage'));
    } finally {
      setIsSending(false);
    }
  };

  if (!isAuthenticated && !isOwner) {
    return null;
  }

  return (
    <>
      {!isOwner && (
        <div className="flex flex-col md:flex-row gap-3">
          <Button
            onClick={() => setMessageDialogOpen(true)}
            className="md:flex-1"
          >
            <MessageSquare className="mr-2 h-5 w-5" />
            {t('listings.sendMessage')}
          </Button>

          <div className="grid grid-cols-2 gap-3">
            <Button
              variant="outline"
              onClick={onSaveListing}
            >
              <Heart className={`mr-2 h-5 w-5 ${isSaved ? 'fill-current' : ''}`} />
              {isSaved ? t('listings.saved') : t('listings.save')}
            </Button>

            <Button
              variant="outline"
              onClick={() => setPhoneDialogOpen(true)}
            >
              <Phone className="mr-2 h-5 w-5" />
              {t('listings.call')}
            </Button>
          </div>
        </div>
      )}

      {onDelete && (
        <Button
          variant="destructive"
          onClick={onDelete}
          disabled={isDeleting}
          className="mt-3 w-full"
        >
          {isDeleting ? t('listings.deleting') : t('listings.deleteListing')}
        </Button>
      )}

      {/* Message Dialog */}
      <Dialog open={messageDialogOpen} onOpenChange={setMessageDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>{t('listings.sendMessage')}</DialogTitle>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <Textarea
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              placeholder={t('listings.typeYourMessage')}
              rows={5}
            />
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setMessageDialogOpen(false)}>
              {t('ui.cancel')}
            </Button>
            <Button
              onClick={handleSendMessage}
              disabled={!message.trim() || isSending}
            >
              {isSending ? t('listings.sending') : t('listings.send')}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Phone Dialog */}
      <Dialog open={phoneDialogOpen} onOpenChange={setPhoneDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>{t('listings.sellerContact')}</DialogTitle>
          </DialogHeader>
          <div className="flex items-center justify-center py-6">
            <div className="text-center">
              <Phone className="h-12 w-12 mx-auto mb-4 text-primary" />
              <p className="text-xl font-bold">{sellerPhone}</p>
              <p className="text-muted-foreground mt-2">{t('listings.contactSellerByPhone')}</p>
            </div>
          </div>
          <DialogFooter>
            <Button
              onClick={() => window.open(`tel:${sellerPhone}`, '_blank')}
              className="w-full"
            >
              {t('listings.callNow')}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default ListingDetailActions;
