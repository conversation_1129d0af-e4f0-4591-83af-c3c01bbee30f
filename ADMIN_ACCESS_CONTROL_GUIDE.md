# Руководство по системе контроля доступа для административной панели

## Обзор

Система контроля доступа обеспечивает безопасность административной панели AutoMarketplace, гарантируя, что только авторизованные пользователи могут получить доступ к административным функциям.

## Архитектура системы

### 1. Уровень базы данных
- **Таблица profiles**: Содержит поля `is_admin` и `role` для определения прав пользователя
- **RLS политики**: Автоматически ограничивают доступ к административным данным
- **Функция `is_user_admin()`**: Централизованная проверка административных прав

### 2. Уровень API
- **Middleware**: Проверка прав доступа для API эндпоинтов
- **Защищенные функции**: Обертки для административных операций

### 3. Уровень UI
- **Хуки**: `useAdminAccess`, `useAdminPermissions`
- **Компоненты**: `AdminGuard`, `ProtectedAction`, `ProtectedContent`
- **Маршруты**: Защищенные административные страницы

## Основные компоненты

### useAdminAccess
Централизованный хук для проверки административных прав:

```typescript
const { isAdmin, isLoading, error, refreshAdminStatus } = useAdminAccess();
```

### AdminGuard
Компонент для условного отображения административного контента:

```tsx
<AdminGuard fallback={<div>Доступ запрещен</div>}>
  <AdminPanel />
</AdminGuard>
```

### ProtectedAction
Компонент для защищенных действий:

```tsx
<ProtectedAction
  action={handleDeleteUser}
  requireAdmin={true}
  confirmMessage="Удалить пользователя?"
  successMessage="Пользователь удален"
>
  Удалить
</ProtectedAction>
```

## Использование

### 1. Защита страниц
```tsx
// В App.tsx или роутере
<Route 
  path="/admin" 
  element={
    <AdminAuth>
      <AdminDashboard />
    </AdminAuth>
  } 
/>
```

### 2. Защита UI элементов
```tsx
// Скрытие административных ссылок
<AdminGuard>
  <Link to="/admin">Админ панель</Link>
</AdminGuard>

// Условный рендеринг
const { isAdmin } = useAdminAccess();
return (
  <div>
    {isAdmin && <AdminToolbar />}
    <RegularContent />
  </div>
);
```

### 3. Защита действий
```tsx
// Защищенная кнопка
<ProtectedAction
  action={async () => await deleteUser(userId)}
  requireAdmin={true}
  confirmMessage="Вы уверены?"
  variant="destructive"
>
  Удалить пользователя
</ProtectedAction>

// Защищенная операция
const handleAdminAction = async () => {
  try {
    await withAdminAccess(async (user) => {
      // Административная операция
      await performAdminTask();
    });
  } catch (error) {
    toast.error('Недостаточно прав');
  }
};
```

### 4. Проверка разрешений
```tsx
const { permissions, hasPermission } = useAdminPermissions();

// Проверка конкретного разрешения
if (hasPermission('canManageUsers')) {
  // Показать интерфейс управления пользователями
}

// Проверка нескольких разрешений
if (permissions.canManageUsers && permissions.canViewReports) {
  // Показать расширенную панель
}
```

## Безопасность

### Уровни защиты

1. **Маршрутизация**: `AdminAuth` компонент проверяет права при загрузке страницы
2. **UI элементы**: `AdminGuard` скрывает административные элементы
3. **Действия**: `ProtectedAction` проверяет права перед выполнением
4. **API**: Middleware проверяет права на сервере
5. **База данных**: RLS политики ограничивают доступ к данным

### Принципы безопасности

- **Принцип наименьших привилегий**: Пользователи получают только необходимые права
- **Защита в глубину**: Множественные уровни проверки
- **Fail-safe**: При ошибке доступ запрещается
- **Аудит**: Все административные действия логируются

## Настройка ролей

### Создание администратора
```sql
-- Через SQL
UPDATE profiles 
SET is_admin = true, role = 'admin' 
WHERE id = 'user-id';

-- Через код (при регистрации)
const { error } = await supabase
  .from('profiles')
  .upsert({
    id: user.id,
    is_admin: email === '<EMAIL>',
    role: email === '<EMAIL>' ? 'admin' : 'user'
  });
```

### Проверка прав в коде
```typescript
// Проверка через хук
const { isAdmin } = useAdminAccess();

// Проверка через API
const { isAdmin } = await checkAdminAccess();

// Проверка через middleware
await requireAdmin(); // Бросает ошибку если не админ
```

## Миграции

Система включает миграции для:
- Унификации полей `is_admin` и `role`
- Создания функции `is_user_admin()`
- Обновления RLS политик
- Синхронизации административных прав

## Тестирование

### Тестовые сценарии

1. **Неавторизованный доступ**: Проверка редиректа на страницу входа
2. **Обычный пользователь**: Проверка скрытия административных элементов
3. **Администратор**: Проверка доступа ко всем функциям
4. **Ошибки сети**: Проверка обработки ошибок при проверке прав

### Примеры тестов
```typescript
// Тест хука
test('useAdminAccess returns correct admin status', async () => {
  const { result } = renderHook(() => useAdminAccess());
  await waitFor(() => {
    expect(result.current.isAdmin).toBe(true);
  });
});

// Тест компонента
test('AdminGuard hides content for non-admin', () => {
  render(
    <AdminGuard>
      <div data-testid="admin-content">Admin only</div>
    </AdminGuard>
  );
  expect(screen.queryByTestId('admin-content')).not.toBeInTheDocument();
});
```

## Устранение неполадок

### Частые проблемы

1. **Пользователь не видит админ панель**
   - Проверить поле `is_admin` в таблице `profiles`
   - Проверить RLS политики
   - Очистить кэш браузера

2. **Ошибки при проверке прав**
   - Проверить подключение к базе данных
   - Проверить функцию `is_user_admin()`
   - Проверить логи ошибок

3. **Медленная загрузка админ панели**
   - Оптимизировать запросы к базе данных
   - Добавить кэширование статуса администратора
   - Использовать индексы в базе данных

### Отладка
```typescript
// Включить отладку
const { isAdmin, error } = useAdminAccess();
console.log('Admin status:', { isAdmin, error });

// Проверить права напрямую
const result = await supabase.rpc('is_user_admin', { user_id: userId });
console.log('Direct check:', result);
```

## Расширение системы

### Добавление новых ролей
1. Обновить enum в базе данных
2. Добавить проверки в `useAdminPermissions`
3. Создать новые компоненты защиты
4. Обновить RLS политики

### Детализированные разрешения
```typescript
// Пример расширенной системы разрешений
enum Permission {
  MANAGE_USERS = 'manage_users',
  MANAGE_LISTINGS = 'manage_listings',
  VIEW_ANALYTICS = 'view_analytics',
  MANAGE_SETTINGS = 'manage_settings'
}

const usePermissions = () => {
  // Логика проверки детализированных разрешений
};
```

## Заключение

Система контроля доступа обеспечивает надежную защиту административной панели на всех уровнях приложения. Следуйте принципам безопасности и регулярно проверяйте права доступа пользователей.
