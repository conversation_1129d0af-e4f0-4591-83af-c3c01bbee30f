
import React from 'react';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { Link, useNavigate } from 'react-router-dom';
import { useLanguage } from '@/hooks/useLanguage';
import { formatCurrency } from '@/utils/dictionaries';
import { Card, CardContent } from '@/components/ui/card';
import { Heart, Flag } from 'lucide-react';

interface ListingPriceCardProps {
  price: number;
  featured: boolean;
  isAuthenticated: boolean;
  isOwner: boolean;
  sellerPhone?: string;
  onSaveListing?: () => void;
  isSaved?: boolean;
  isMobile?: boolean;
  sellerId?: string;
  listingId?: string;
  onReportClick?: () => void;
}

const ListingPriceCard: React.FC<ListingPriceCardProps> = ({
  price,
  featured,
  isAuthenticated,
  isOwner,
  onSaveListing,
  isSaved,
  isMobile = false,
  onReportClick,
}) => {
  const navigate = useNavigate();
  const { t } = useLanguage();

  // Pre-translate all text to ensure correct rendering
  const featuredText = t('listings.featured');
  const signInText = t('listings.signInToContactSeller');
  const saveText = t('listings.save');
  const savedText = t('listings.saved');
  const reportListingText = t('listings.reportListing');

  return (
    <Card>
      <CardContent className="p-6">
        <div className="text-3xl font-bold mb-4">
          {formatCurrency(price)}
        </div>
        {featured && (
          <Badge className="mb-4" variant="secondary">
            {featuredText}
          </Badge>
        )}
        <Separator className="my-4" />

        {/* Authentication/save button */}
        {!isAuthenticated && (
          <Button asChild className="w-full">
            <Link to="/auth">
              {signInText}
            </Link>
          </Button>
        )}

        {isAuthenticated && !isOwner && (
          <div className="space-y-3">
            <Button
              variant="outline"
              onClick={onSaveListing}
              className="w-full"
            >
              <Heart className={`mr-2 h-5 w-5 ${isSaved ? 'fill-current' : ''}`} />
              {isSaved ? savedText : saveText}
            </Button>

            <div className="flex justify-center">
              <Button
                variant="ghost"
                size="sm"
                onClick={onReportClick}
                className="text-muted-foreground hover:text-destructive"
              >
                <Flag className="mr-1 h-4 w-4" />
                {reportListingText}
              </Button>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default ListingPriceCard;
