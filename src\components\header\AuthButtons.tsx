
import React from 'react';
import { Link } from 'react-router-dom';
import { useLanguage } from '@/hooks/useLanguage';
import { Button } from '@/components/ui/button';
import { useAuth } from '@/contexts/AuthContext';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import { LogOut, User } from 'lucide-react';

const AuthButtons: React.FC = () => {
  const { t } = useLanguage();
  const { user, signOut, isAuthenticated } = useAuth();

  if (isAuthenticated) {
    return (
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" size="icon" className="rounded-full">
            <Avatar>
              <AvatarFallback>
                {user?.email?.charAt(0).toUpperCase() || "U"}
              </AvatarFallback>
            </Avatar>
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuLabel>{user?.email}</DropdownMenuLabel>
          <DropdownMenuSeparator />
          <DropdownMenuItem asChild>
            <Link to="/profile" className="cursor-pointer flex items-center">
              <User className="mr-2 h-4 w-4" />
              {t('profile.profile')}
            </Link>
          </DropdownMenuItem>
          <DropdownMenuItem className="cursor-pointer text-destructive" onClick={() => signOut()}>
            <LogOut className="mr-2 h-4 w-4" />
            {t('signOut')}
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    );
  }

  return (
    <div className="flex items-center gap-2">
      <Button variant="ghost" asChild className="text-xs sm:text-sm px-3 py-1 h-8">
        <Link to="/auth">
          {t('signIn')}
        </Link>
      </Button>

      <Button asChild className="text-xs sm:text-sm px-3 py-1 h-8">
        <Link to="/auth?mode=signup">
          {t('signUp')}
        </Link>
      </Button>
    </div>
  );
};

export default AuthButtons;
