
import React from 'react';
import { useLanguage } from '@/hooks/useLanguage';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { carBodyTypes } from '@/utils/dictionaries/vehicles';

interface BodyTypeFilterProps {
  bodyType: string;
  setBodyType: (value: string) => void;
}

const BodyTypeFilter: React.FC<BodyTypeFilterProps> = ({
  bodyType,
  setBodyType
}) => {
  const { t, language } = useLanguage();
  const bodyTypeOptions = carBodyTypes[language as keyof typeof carBodyTypes] || carBodyTypes.en;

  return (
    <div className="space-y-3">
      <h3 className="text-sm font-medium">{t('listings.bodyType')}</h3>
      <Select value={bodyType} onValueChange={setBodyType}>
        <SelectTrigger className="w-full">
          <SelectValue placeholder={t('listings.selectBodyType')} />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="all">{t('listings.all')}</SelectItem>
          {bodyTypeOptions.map((type) => (
            <SelectItem key={type} value={type}>
              {type}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  );
};

export default BodyTypeFilter;
