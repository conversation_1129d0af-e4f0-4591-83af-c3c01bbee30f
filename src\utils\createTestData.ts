import { supabase } from '@/integrations/supabase/client';

export const createTestData = async () => {
  console.log('🔧 Creating test data...');
  
  try {
    // First, check if we have any data
    const { count: existingCount, error: countError } = await supabase
      .from('listings')
      .select('*', { count: 'exact', head: true });
    
    if (countError) {
      console.error('Error checking existing data:', countError);
      throw countError;
    }
    
    console.log(`Found ${existingCount} existing listings`);
    
    // Create test listings with different statuses
    const testListings = [
      {
        title: 'BMW X5 Engine 3.0L Diesel 2018 - APPROVED',
        description: 'Complete engine assembly from BMW X5. Excellent condition, low mileage.',
        make: 'BMW',
        model: 'X5',
        year: 2018,
        price: 3500,
        location: 'Los Angeles, CA',
        featured: true,
        status: 'approved',
        user_id: 'test-user-1',
        image_urls: ['https://images.unsplash.com/photo-1619642751034-765dfdf7c58e?auto=format&fit=crop&q=80&w=1974&ixlib=rb-4.0.3']
      },
      {
        title: 'Toyota Camry Transmission 2020 - PENDING',
        description: 'Automatic transmission from Toyota Camry 2020. Perfect working condition.',
        make: 'Toyota',
        model: 'Camry',
        year: 2020,
        price: 1200,
        location: 'New York, NY',
        featured: false,
        status: 'pending',
        user_id: 'test-user-2',
        image_urls: ['https://images.unsplash.com/photo-1617814076367-b759c7d7e738?auto=format&fit=crop&q=80&w=2574&ixlib=rb-4.0.3']
      },
      {
        title: 'Mercedes E-Class Front Bumper 2019 - APPROVED',
        description: 'Original front bumper from Mercedes E-Class 2019. Minor scratches.',
        make: 'Mercedes-Benz',
        model: 'E-Class',
        year: 2019,
        price: 850,
        location: 'Chicago, IL',
        featured: true,
        status: 'approved',
        user_id: 'test-user-3',
        image_urls: ['https://images.unsplash.com/photo-1533473359331-0135ef1b58bf?auto=format&fit=crop&q=80&w=2670&ixlib=rb-4.0.3']
      },
      {
        title: 'Ford F-150 Tailgate 2021 - PENDING',
        description: 'Original tailgate from Ford F-150 2021. Excellent condition.',
        make: 'Ford',
        model: 'F-150',
        year: 2021,
        price: 650,
        location: 'Dallas, TX',
        featured: false,
        status: 'pending',
        user_id: 'test-user-4',
        image_urls: ['https://images.unsplash.com/photo-1558618666-fcd25c85cd64?auto=format&fit=crop&q=80&w=2670&ixlib=rb-4.0.3']
      },
      {
        title: 'Honda Civic Headlights Set 2019 - APPROVED',
        description: 'Complete LED headlights set from Honda Civic 2019.',
        make: 'Honda',
        model: 'Civic',
        year: 2019,
        price: 450,
        location: 'Miami, FL',
        featured: false,
        status: 'approved',
        user_id: 'test-user-5',
        image_urls: ['https://images.unsplash.com/photo-1552519507-da3b142c6e3d?auto=format&fit=crop&q=80&w=2670&ixlib=rb-4.0.3']
      }
    ];
    
    console.log('Inserting test listings...');
    
    // Insert test listings
    const { data, error } = await supabase
      .from('listings')
      .insert(testListings)
      .select();
    
    if (error) {
      console.error('Error inserting test data:', error);
      throw error;
    }
    
    console.log('✅ Test data created successfully:', data);
    
    // Verify the data was inserted
    const { count: newCount, error: verifyError } = await supabase
      .from('listings')
      .select('*', { count: 'exact', head: true });
    
    if (verifyError) {
      console.error('Error verifying data:', verifyError);
      throw verifyError;
    }
    
    console.log(`✅ Total listings after insert: ${newCount}`);
    
    return {
      success: true,
      inserted: testListings.length,
      total: newCount
    };
    
  } catch (error) {
    console.error('❌ Error creating test data:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

// Function to check database connection
export const checkDatabaseConnection = async () => {
  console.log('🔍 Checking database connection...');
  
  try {
    const { data, error } = await supabase
      .from('listings')
      .select('id, title, make, model, price, status')
      .limit(5);
    
    if (error) {
      console.error('Database connection error:', error);
      return { success: false, error: error.message };
    }
    
    console.log('✅ Database connection successful');
    console.log('Sample data:', data);
    
    return { 
      success: true, 
      data,
      count: data.length 
    };
    
  } catch (error) {
    console.error('❌ Database connection failed:', error);
    return { 
      success: false, 
      error: error.message 
    };
  }
};

// Make functions available globally for browser console testing
if (typeof window !== 'undefined') {
  (window as any).createTestData = createTestData;
  (window as any).checkDatabaseConnection = checkDatabaseConnection;
}
