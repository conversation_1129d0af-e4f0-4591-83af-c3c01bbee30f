
import { createRoot } from 'react-dom/client'
import App from './App.tsx'
import './index.css'
import '@/utils/i18n/index.ts' // Ensure i18n is initialized

// Убираем сброс пути, чтобы навигация работала корректно
// if (window.location.pathname !== '/' && !window.location.pathname.includes('?')) {
//   window.history.replaceState({}, '', '/');
// }

createRoot(document.getElementById("root")!).render(<App />);
