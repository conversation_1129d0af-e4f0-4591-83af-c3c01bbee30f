import React from 'react';

interface Listing {
  id: string;
  title: string;
  make: string;
  model: string;
  price: number;
  featured: boolean;
  created_at: string;
}

interface SimpleListingTableProps {
  listings: Listing[];
}

const SimpleListingTable: React.FC<SimpleListingTableProps> = ({ listings }) => {
  console.log('SimpleListingTable render:', {
    listingsCount: listings.length,
    listings: listings.slice(0, 3)
  });

  return (
    <div style={{ border: '2px solid red', padding: '20px', margin: '20px' }}>
      <h3 style={{ color: 'red', marginBottom: '10px' }}>
        Simple Debug Table (Count: {listings.length})
      </h3>
      
      {listings.length === 0 ? (
        <p style={{ color: 'red', fontSize: '18px' }}>NO LISTINGS FOUND!</p>
      ) : (
        <table style={{ 
          width: '100%', 
          border: '1px solid black',
          borderCollapse: 'collapse'
        }}>
          <thead>
            <tr style={{ backgroundColor: '#f0f0f0' }}>
              <th style={{ border: '1px solid black', padding: '10px' }}>ID</th>
              <th style={{ border: '1px solid black', padding: '10px' }}>Title</th>
              <th style={{ border: '1px solid black', padding: '10px' }}>Make</th>
              <th style={{ border: '1px solid black', padding: '10px' }}>Model</th>
              <th style={{ border: '1px solid black', padding: '10px' }}>Price</th>
            </tr>
          </thead>
          <tbody>
            {listings.map((listing, index) => {
              console.log(`Rendering simple row ${index}:`, listing);
              return (
                <tr 
                  key={listing.id}
                  style={{ 
                    backgroundColor: index % 2 === 0 ? '#ffffff' : '#f9f9f9',
                    minHeight: '50px'
                  }}
                >
                  <td style={{ 
                    border: '1px solid black', 
                    padding: '10px',
                    color: 'black',
                    fontSize: '14px'
                  }}>
                    {listing.id}
                  </td>
                  <td style={{ 
                    border: '1px solid black', 
                    padding: '10px',
                    color: 'black',
                    fontSize: '14px',
                    fontWeight: 'bold'
                  }}>
                    {listing.title || 'NO TITLE'}
                  </td>
                  <td style={{ 
                    border: '1px solid black', 
                    padding: '10px',
                    color: 'black',
                    fontSize: '14px'
                  }}>
                    {listing.make || 'NO MAKE'}
                  </td>
                  <td style={{ 
                    border: '1px solid black', 
                    padding: '10px',
                    color: 'black',
                    fontSize: '14px'
                  }}>
                    {listing.model || 'NO MODEL'}
                  </td>
                  <td style={{ 
                    border: '1px solid black', 
                    padding: '10px',
                    color: 'black',
                    fontSize: '14px'
                  }}>
                    ${listing.price ? listing.price.toLocaleString() : '0'}
                  </td>
                </tr>
              );
            })}
          </tbody>
        </table>
      )}
      
      <div style={{ marginTop: '10px', fontSize: '12px', color: '#666' }}>
        <p>Debug info:</p>
        <p>- Array.isArray(listings): {Array.isArray(listings).toString()}</p>
        <p>- typeof listings: {typeof listings}</p>
        <p>- listings.length: {listings.length}</p>
        <p>- First listing: {JSON.stringify(listings[0] || 'none')}</p>
      </div>
    </div>
  );
};

export default SimpleListingTable;
