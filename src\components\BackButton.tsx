
import React from 'react';
import { Button } from '@/components/ui/button';
import { ArrowLeft } from 'lucide-react';
import { Link } from 'react-router-dom';
import { useLanguage } from '@/hooks/useLanguage';

interface BackButtonProps {
  to: string;
  text?: string;
  className?: string;
}

const BackButton = ({ to, text, className = '' }: BackButtonProps) => {
  const { t } = useLanguage();
  
  // Use provided text or default back text
  const buttonText = text || t('ui.back');

  return (
    <Button
      variant="ghost"
      asChild
      size="sm"
      className={`gap-1 mb-4 pl-0 hover:pl-1 transition-all ${className}`}
    >
      <Link to={to}>
        <ArrowLeft className="h-4 w-4" />
        {buttonText}
      </Link>
    </Button>
  );
};

export default BackButton;
