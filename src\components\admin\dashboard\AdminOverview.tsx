
import React, { useState, useEffect } from 'react';
import { useLanguage } from '@/hooks/useLanguage';
import { toast } from 'sonner';
import AdminStats from '@/components/admin/AdminStats';
import { useSettings } from '@/hooks/useSettings';
import { useListings } from '@/hooks/useListings';
import PendingListings from '@/components/admin/dashboard/pending-listings';
import NewListings from '@/components/admin/dashboard/new-listings';
import RecentActivity from './RecentActivity';
import { Listing } from '@/hooks/listings/types';

const AdminOverview: React.FC = () => {
  const { t } = useLanguage();
  const { settings, updateSettings } = useSettings();
  const { listings, isLoading, fetchListings, updateListingStatus } = useListings();
  const [autoApprove, setAutoApprove] = useState(settings.listings?.auto_approve || false);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [pendingListings, setPendingListings] = useState<Listing[]>([]);
  const [newListings, setNewListings] = useState<Listing[]>([]);
  
  useEffect(() => {
    setAutoApprove(settings.listings?.auto_approve || false);
  }, [settings]);
  
  useEffect(() => {
    // Filter listings for different sections
    // Pending listings: status is pending or undefined
    const pending = listings.filter(listing => listing.status === 'pending' || !listing.status);
    setPendingListings(pending);
    
    // New listings: most recent listings, regardless of status (different from pending)
    const recent = [...listings]
      .sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())
      .filter(listing => !(listing.status === 'pending' || !listing.status)) // Exclude pending listings
      .slice(0, 10); // Get more items for pagination
    setNewListings(recent);
  }, [listings]);
  
  const handleAutoApproveChange = async (checked: boolean) => {
    try {
      // Set the state first for immediate UI feedback
      setAutoApprove(checked);
      
      // Update the settings in the backend
      await updateSettings('listings', { auto_approve: checked });
      
      if (checked) {
        toast.success(t('admin.autoApproveEnabled'));
      } else {
        toast.success(t('admin.autoApproveDisabled'));
      }
      
      // If auto-approve is turned on, approve all pending listings
      if (checked) {
        const approvalPromises = pendingListings.map(listing => 
          updateListingStatus(listing.id, 'approved')
        );
        
        await Promise.all(approvalPromises);
        
        // Force refresh listings
        await fetchListings();
        
        toast.success(t('admin.allListingsApproved'));
      }
    } catch (error) {
      console.error('Error updating auto-approve setting:', error);
      toast.error(t('admin.errorSavingSettings'));
      // Revert UI state on error
      setAutoApprove(!checked);
    }
  };
  
  const handleRefresh = async () => {
    setIsRefreshing(true);
    try {
      await fetchListings();
      toast.success(t('admin.listingsRefreshed'));
    } catch (error) {
      console.error('Error refreshing listings:', error);
      toast.error(t('admin.errorRefreshingListings'));
    } finally {
      setIsRefreshing(false);
    }
  };
  
  const handleApproveListing = async (id: string) => {
    try {
      await updateListingStatus(id, 'approved');
      toast.success(t('admin.listingApproved'));
      
      // Update our local state to reflect the change
      setPendingListings(prev => prev.filter(item => item.id !== id));
      
      // Add the approved listing to the newListings array at the top
      const approvedListing = pendingListings.find(item => item.id === id);
      if (approvedListing) {
        setNewListings(prev => [{...approvedListing, status: 'approved'}, ...prev]);
      }
    } catch (error) {
      console.error('Error approving listing:', error);
      toast.error(t('admin.errorApprovingListing'));
    }
  };
  
  return (
    <div className="space-y-6">
      <AdminStats />
      
      <PendingListings 
        pendingListings={pendingListings.map(listing => ({
          ...listing,
          onApprove: () => handleApproveListing(listing.id)
        }))}
        isLoading={isLoading}
        isRefreshing={isRefreshing}
        autoApprove={autoApprove}
        onAutoApproveChange={handleAutoApproveChange}
        onRefresh={handleRefresh}
      />
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <NewListings 
          listings={newListings}
          onApprove={handleApproveListing}
          isLoading={isLoading}
        />
        <RecentActivity />
      </div>
    </div>
  );
};

export default AdminOverview;
