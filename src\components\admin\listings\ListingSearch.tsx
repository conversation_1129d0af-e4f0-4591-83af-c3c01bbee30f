
import React from 'react';
import { useLanguage } from '@/hooks/useLanguage';
import { Input } from '@/components/ui/input';
import { Search } from 'lucide-react';

interface ListingSearchProps {
  searchTerm: string;
  setSearchTerm: (value: string) => void;
}

const ListingSearch: React.FC<ListingSearchProps> = ({ searchTerm, setSearchTerm }) => {
  const { t } = useLanguage();
  
  return (
    <div className="relative flex-grow max-w-md">
      <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
      <Input
        placeholder={t('listings.search')}
        value={searchTerm}
        onChange={(e) => setSearchTerm(e.target.value)}
        className="pl-10"
      />
    </div>
  );
};

export default ListingSearch;
