
import React from 'react';
import { Card, CardContent, CardTitle } from '@/components/ui/card';
import { useLanguage } from '@/hooks/useLanguage';
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';

interface StatsCardProps {
  title: string;
  value: number | string;
  isLoading?: boolean;
}

const StatsCard = ({ title, value, isLoading = false }: StatsCardProps) => {
  return (
    <Card className="shadow-sm">
      <CardContent className="pt-6">
        <CardTitle className="text-muted-foreground text-sm font-medium mb-2">
          {title}
        </CardTitle>

        <div className="text-2xl font-bold">
          {isLoading ?
            <div className="h-8 w-16 rounded bg-muted animate-pulse"></div> :
            value
          }
        </div>
      </CardContent>
    </Card>
  );
};

const AdminStats: React.FC = () => {
  const { t } = useLanguage();

  // Improved query with error handling
  const { data: totalListings, isLoading: isLoadingListings } = useQuery({
    queryKey: ['admin-total-listings'],
    queryFn: async () => {
      try {
        const { count, error } = await supabase
          .from('listings')
          .select('*', { count: 'exact', head: true });

        if (error) throw error;
        return count || 0;
      } catch (error) {
        console.error('Error fetching listings count:', error);
        return 0;
      }
    }
  });

  // Improved query with error handling
  const { data: totalUsers, isLoading: isLoadingUsers } = useQuery({
    queryKey: ['admin-total-users'],
    queryFn: async () => {
      try {
        const { count, error } = await supabase
          .from('profiles')
          .select('*', { count: 'exact', head: true });

        if (error) throw error;
        return count || 0;
      } catch (error) {
        console.error('Error fetching users count:', error);
        return 0;
      }
    }
  });

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      <StatsCard
        title={t('admin.totalListings')}
        value={totalListings || 0}
        isLoading={isLoadingListings}
      />
      <StatsCard
        title={t('admin.totalUsers')}
        value={totalUsers || 0}
        isLoading={isLoadingUsers}
      />
      <StatsCard
        title={t('admin.activeListings')}
        value="78%"
      />
      <StatsCard
        title={t('admin.totalRevenue')}
        value="$8,450"
      />
    </div>
  );
};

export default AdminStats;
