
import React from 'react';
import { Badge } from '@/components/ui/badge';
import { BadgeCheck, BadgeDollarSign } from 'lucide-react';
import { useLanguage } from '@/hooks/useLanguage';

interface ListingStatusBadgesProps {
  isVip?: boolean;
  isPaid?: boolean;
  className?: string;
}

const ListingStatusBadges: React.FC<ListingStatusBadgesProps> = ({
  isVip,
  isPaid,
  className = ''
}) => {
  const { t } = useLanguage();
  
  if (!isVip && !isPaid) return null;
  
  return (
    <div className={`flex gap-2 ${className}`}>
      {isVip && (
        <Badge variant="secondary" className="bg-blue-100 text-blue-800 flex items-center">
          <BadgeCheck className="h-3 w-3 mr-1" />
          VIP
        </Badge>
      )}
      
      {isPaid && (
        <Badge variant="secondary" className="bg-green-100 text-green-800 flex items-center">
          <BadgeDollarSign className="h-3 w-3 mr-1" />
          {t('admin.paid')}
        </Badge>
      )}
    </div>
  );
};

export default ListingStatusBadges;
