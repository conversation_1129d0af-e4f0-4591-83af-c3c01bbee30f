
import React, { useEffect, useState } from 'react';
import { Link } from 'react-router-dom';
import { useLanguage } from '@/hooks/useLanguage';
import { useIsMobile } from '@/hooks/use-mobile';

const Logo: React.FC = () => {
  const { t, language } = useLanguage();
  const isMobile = useIsMobile();
  const [currentLanguage, setCurrentLanguage] = useState<string>(language);

  // Отслеживаем изменения языка
  useEffect(() => {
    setCurrentLanguage(language);
    console.log('Logo language updated:', language);
  }, [language]);

  return (
    <Link
      to="/"
      className="flex items-center space-x-2"
      aria-label="Homepage"
    >
      <img
        src="/lovable-uploads/1bb17488-9834-4635-adaa-9396a26c931e.png"
        alt="LA Logo"
        className="h-8 w-auto"
      />
      {isMobile ? (
        <div className="flex flex-col leading-tight">
          {currentLanguage === 'ru' ? (
            <>
              <span className="text-sm font-semibold tracking-tight">Авторынок</span>
              <span className="text-sm font-semibold tracking-tight">запчастей</span>
            </>
          ) : (
            <>
              <span className="text-sm font-semibold tracking-tight">Auto</span>
              <span className="text-sm font-semibold tracking-tight">Parts</span>
            </>
          )}
        </div>
      ) : (
        <span className="text-xl md:text-2xl font-semibold tracking-tight">
          {t('appName')}
        </span>
      )}
    </Link>
  );
};

export default Logo;
