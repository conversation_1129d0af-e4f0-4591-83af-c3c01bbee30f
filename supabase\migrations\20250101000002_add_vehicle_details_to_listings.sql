-- Add vehicle detail columns to listings table
-- These columns store additional vehicle information for better filtering and search

-- Add vehicle specification columns
ALTER TABLE public.listings 
ADD COLUMN body_type TEXT,
ADD COLUMN fuel_type TEXT,
ADD COLUMN transmission TEXT,
ADD COLUMN color TEXT,
ADD COLUMN mileage INTEGER;

-- Add indexes for better performance when filtering
CREATE INDEX idx_listings_body_type ON public.listings(body_type);
CREATE INDEX idx_listings_fuel_type ON public.listings(fuel_type);
CREATE INDEX idx_listings_transmission ON public.listings(transmission);
CREATE INDEX idx_listings_color ON public.listings(color);
CREATE INDEX idx_listings_mileage ON public.listings(mileage);

-- Add comments to explain the new fields
COMMENT ON COLUMN public.listings.body_type IS 'Vehicle body type (e.g., sedan, SUV, hatchback)';
COMMENT ON COLUMN public.listings.fuel_type IS 'Vehicle fuel type (e.g., gasoline, diesel, electric)';
COMMENT ON COLUMN public.listings.transmission IS 'Vehicle transmission type (e.g., manual, automatic)';
COMMENT ON COLUMN public.listings.color IS 'Vehicle color';
COMMENT ON COLUMN public.listings.mileage IS 'Vehicle mileage in kilometers or miles';
