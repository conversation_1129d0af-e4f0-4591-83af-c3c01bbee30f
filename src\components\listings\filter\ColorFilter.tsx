
import React from 'react';
import { useLanguage } from '@/hooks/useLanguage';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { carColors } from '@/utils/dictionaries/vehicles';

interface ColorFilterProps {
  color: string;
  setColor: (value: string) => void;
}

const ColorFilter: React.FC<ColorFilterProps> = ({
  color,
  setColor
}) => {
  const { t, language } = useLanguage();
  const colorOptions = carColors[language as keyof typeof carColors] || carColors.en;

  return (
    <div className="space-y-3">
      <h3 className="text-sm font-medium">{t('listings.color')}</h3>
      <Select value={color} onValueChange={setColor}>
        <SelectTrigger className="w-full">
          <SelectValue placeholder={t('listings.selectColor')} />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="all">{t('listings.all')}</SelectItem>
          {colorOptions.map((carColor) => (
            <SelectItem key={carColor} value={carColor}>
              {carColor}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  );
};

export default ColorFilter;
