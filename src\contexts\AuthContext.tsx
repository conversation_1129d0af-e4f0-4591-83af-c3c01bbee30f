
import React, { createContext, useContext, useEffect, useState } from 'react';
import { User, Session } from '@supabase/supabase-js';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/components/ui/use-toast';
import { toast as sonnerToast } from 'sonner';

type AuthContextType = {
  user: User | null;
  session: Session | null;
  isLoading: boolean;
  signIn: (email: string, password: string) => Promise<void>;
  signInWithGoogle: () => Promise<void>;
  signUp: (email: string, password: string, firstName: string, lastName: string) => Promise<void>;
  signOut: () => Promise<void>;
  isAuthenticated: boolean;
};

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [session, setSession] = useState<Session | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const { toast } = useToast();

  useEffect(() => {
    const fetchSession = async () => {
      try {
        const { data: { session }, error } = await supabase.auth.getSession();

        if (error) {
          console.error('Error fetching session:', error);
        } else {
          setSession(session);
          setUser(session?.user || null);
        }
      } catch (error) {
        console.error('Unexpected error fetching session:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchSession();

    const { data: { subscription } } = supabase.auth.onAuthStateChange((_event, session) => {
      setSession(session);
      setUser(session?.user || null);
      setIsLoading(false);
    });

    return () => subscription.unsubscribe();
  }, []);

  const signIn = async (email: string, password: string) => {
    try {
      setIsLoading(true);
      const { error } = await supabase.auth.signInWithPassword({ email, password });

      if (error) {
        console.error('Login error:', error);
        sonnerToast.error("Login error", {
          description: error.message,
        });
        throw error;
      }

      sonnerToast.success("Login successful", {
        description: "You've successfully logged in"
      });
    } catch (error) {
      console.error('Error signing in:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const signInWithGoogle = async () => {
    try {
      setIsLoading(true);
      const { error } = await supabase.auth.signInWithOAuth({
        provider: 'google',
        options: {
          redirectTo: window.location.origin + '/auth'
        }
      });

      if (error) {
        console.error('Google login error:', error);
        sonnerToast.error("Google login error", {
          description: error.message,
        });
        throw error;
      }
    } catch (error) {
      console.error('Error signing in with Google:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const signUp = async (email: string, password: string, firstName: string, lastName: string) => {
    try {
      setIsLoading(true);

      // Validate input
      if (!email || !password || !firstName || !lastName) {
        sonnerToast.error("Registration error", {
          description: "All fields are required"
        });
        return;
      }

      if (password.length < 6) {
        sonnerToast.error("Registration error", {
          description: "Password must be at least 6 characters long"
        });
        return;
      }

      // Validate email format
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(email)) {
        sonnerToast.error("Registration error", {
          description: "Please enter a valid email address"
        });
        return;
      }

      // Create new user
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            full_name: `${firstName} ${lastName}`,
            username: email.split('@')[0]
          },
          emailRedirectTo: window.location.origin + '/auth',
        }
      });

      if (error) {
        console.error('Registration error:', error);

        // Handle specific error cases
        if (error.message.includes('already registered')) {
          sonnerToast.error("Registration error", {
            description: "An account with this email already exists. Please sign in instead."
          });
        } else if (error.message.includes('Password')) {
          sonnerToast.error("Registration error", {
            description: "Password must be at least 6 characters long"
          });
        } else if (error.message.includes('Email')) {
          sonnerToast.error("Registration error", {
            description: "Please enter a valid email address"
          });
        } else {
          sonnerToast.error("Registration error", {
            description: error.message
          });
        }
        throw error;
      }

      if (data && data.user) {
        // Add the user to the profiles table if they're new
        if (data.user.id) {
          const { error: profileError } = await supabase
            .from('profiles')
            .upsert({
              id: data.user.id,
              username: email.split('@')[0],
              full_name: `${firstName} ${lastName}`,
              is_admin: email === '<EMAIL>'
            });

          if (profileError) {
            console.error('Error creating profile:', profileError);
            sonnerToast.warning("Profile creation warning", {
              description: "Account created but profile setup incomplete. Please contact support if you experience issues."
            });
          }
        }

        // Check if email confirmation is required
        if (data.user.email_confirmed_at) {
          sonnerToast.success("Registration successful", {
            description: "You've successfully registered and are now logged in"
          });
        } else {
          sonnerToast.success("Registration successful", {
            description: "Account created! You can now sign in."
          });

          // Try to sign in automatically if no email confirmation is required
          try {
            await signIn(email, password);
          } catch (signInError) {
            console.log('Auto sign-in failed, user will need to sign in manually');
          }
        }
      } else {
        sonnerToast.info("Email confirmation required", {
          description: "Please check your email to confirm your registration before signing in"
        });
      }
    } catch (error) {
      console.error('Error signing up:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const signOut = async () => {
    try {
      const { error } = await supabase.auth.signOut();
      if (error) {
        sonnerToast.error("Sign out error", {
          description: error.message
        });
        throw error;
      }
      sonnerToast.success("Signed out", {
        description: "You've been successfully signed out"
      });
    } catch (error) {
      console.error('Error signing out:', error);
      throw error;
    }
  };

  return (
    <AuthContext.Provider value={{
      user,
      session,
      isLoading,
      signIn,
      signInWithGoogle,
      signUp,
      signOut,
      isAuthenticated: !!user
    }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
