
import React from 'react';
import { useLanguage } from '@/hooks/useLanguage';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { User, Star, CreditCard } from 'lucide-react';
import UserRoleBadge from './UserRoleBadge';
import UserStatusBadge from './UserStatusBadge';
import UserActionMenu from './UserActionMenu';
import { Badge } from '@/components/ui/badge';

interface UserData {
  id: string;
  username: string;
  email: string;
  fullName: string;
  role: string;
  status: string;
  registrationDate: string;
  lastLogin: string;
  vipStatus: boolean;
  paidStatus: boolean;
}

interface UserTableProps {
  users: UserData[];
  formatDate: (dateString: string) => string;
  onRoleChange: (userId: string, newRole: string) => void;
  onStatusChange: (userId: string, newStatus: string) => void;
  onPasswordReset: (userId: string) => void;
  onVipStatusChange?: (userId: string, newStatus: boolean) => void;
  onPaidStatusChange?: (userId: string, newStatus: boolean) => void;
}

const UserTable: React.FC<UserTableProps> = ({
  users,
  formatDate,
  onRoleChange,
  onStatusChange,
  onPasswordReset,
  onVipStatusChange,
  onPaidStatusChange,
}) => {
  const { t } = useLanguage();
  
  if (users.length === 0) {
    return null;
  }
  
  return (
    <div className="rounded-md border">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>{t('admin.user')}</TableHead>
            <TableHead>{t('admin.userEmail')}</TableHead>
            <TableHead>{t('admin.role')}</TableHead>
            <TableHead>{t('admin.status')}</TableHead>
            <TableHead>{t('admin.vipStatus')}</TableHead>
            <TableHead>{t('admin.paidStatus')}</TableHead>
            <TableHead>{t('admin.registrationDate')}</TableHead>
            <TableHead>{t('admin.lastLogin')}</TableHead>
            <TableHead>{t('admin.actions')}</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {users.map((user) => (
            <TableRow key={user.id}>
              <TableCell className="font-medium">
                <div className="flex items-center gap-2">
                  <div className="w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center">
                    <User className="h-4 w-4 text-primary" />
                  </div>
                  <div>
                    <div>{user.fullName}</div>
                    <div className="text-xs text-muted-foreground">@{user.username}</div>
                  </div>
                </div>
              </TableCell>
              <TableCell>{user.email}</TableCell>
              <TableCell>
                <UserRoleBadge role={user.role} />
              </TableCell>
              <TableCell>
                <UserStatusBadge status={user.status} />
              </TableCell>
              <TableCell>
                {user.vipStatus ? (
                  <Badge variant="secondary" className="bg-amber-100 text-amber-700 hover:bg-amber-200">
                    <Star className="h-3 w-3 mr-1 fill-amber-500 text-amber-500" />
                    {t('admin.vip')}
                  </Badge>
                ) : (
                  <Badge variant="outline" className="text-muted-foreground">
                    {t('admin.notVip')}
                  </Badge>
                )}
              </TableCell>
              <TableCell>
                {user.paidStatus ? (
                  <Badge variant="secondary" className="bg-emerald-100 text-emerald-700 hover:bg-emerald-200">
                    <CreditCard className="h-3 w-3 mr-1 text-emerald-500" />
                    {t('admin.paid')}
                  </Badge>
                ) : (
                  <Badge variant="outline" className="text-muted-foreground">
                    {t('admin.notPaid')}
                  </Badge>
                )}
              </TableCell>
              <TableCell>{formatDate(user.registrationDate)}</TableCell>
              <TableCell>{formatDate(user.lastLogin)}</TableCell>
              <TableCell>
                <UserActionMenu
                  userId={user.id}
                  status={user.status}
                  vipStatus={user.vipStatus}
                  paidStatus={user.paidStatus}
                  onRoleChange={onRoleChange}
                  onStatusChange={onStatusChange}
                  onPasswordReset={onPasswordReset}
                  onVipStatusChange={onVipStatusChange}
                  onPaidStatusChange={onPaidStatusChange}
                />
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
};

export default UserTable;
