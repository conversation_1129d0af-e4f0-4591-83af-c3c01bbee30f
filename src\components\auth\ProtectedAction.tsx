import React, { ReactNode, useState } from 'react';
import { useAdminAccess } from '@/hooks/useAdminAccess';
import { useAuth } from '@/contexts/AuthContext';
import { toast } from 'sonner';
import { useLanguage } from '@/hooks/useLanguage';
import { Button } from '@/components/ui/button';
import { Shield, AlertTriangle } from 'lucide-react';

interface ProtectedActionProps {
  children: ReactNode;
  action: () => Promise<void> | void;
  requireAuth?: boolean;
  requireAdmin?: boolean;
  confirmMessage?: string;
  successMessage?: string;
  errorMessage?: string;
  className?: string;
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link';
  size?: 'default' | 'sm' | 'lg' | 'icon';
  disabled?: boolean;
}

/**
 * Компонент для защищенных действий, требующих аутентификации или административных прав
 */
export const ProtectedAction: React.FC<ProtectedActionProps> = ({
  children,
  action,
  requireAuth = false,
  requireAdmin = false,
  confirmMessage,
  successMessage,
  errorMessage,
  className,
  variant = 'default',
  size = 'default',
  disabled = false,
}) => {
  const { t } = useLanguage();
  const { isAuthenticated } = useAuth();
  const { isAdmin, isLoading } = useAdminAccess();
  const [isExecuting, setIsExecuting] = useState(false);

  const handleAction = async () => {
    // Проверка аутентификации
    if (requireAuth && !isAuthenticated) {
      toast.error(t('authRequired'), {
        description: t('pleaseSignInToContinue'),
      });
      return;
    }

    // Проверка административных прав
    if (requireAdmin && !isAdmin) {
      toast.error(t('accessDenied'), {
        description: t('adminAccessRequired'),
      });
      return;
    }

    // Подтверждение действия
    if (confirmMessage) {
      const confirmed = window.confirm(confirmMessage);
      if (!confirmed) {
        return;
      }
    }

    try {
      setIsExecuting(true);
      await action();
      
      if (successMessage) {
        toast.success(successMessage);
      }
    } catch (error) {
      console.error('Protected action failed:', error);
      const message = errorMessage || t('actionFailed');
      toast.error(message, {
        description: error instanceof Error ? error.message : t('unknownError'),
      });
    } finally {
      setIsExecuting(false);
    }
  };

  // Определяем, должна ли кнопка быть отключена
  const isDisabled = disabled || 
    isExecuting || 
    (requireAuth && !isAuthenticated) ||
    (requireAdmin && (isLoading || !isAdmin));

  return (
    <Button
      onClick={handleAction}
      disabled={isDisabled}
      className={className}
      variant={variant}
      size={size}
    >
      {isExecuting ? (
        <>
          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current mr-2" />
          {t('processing')}
        </>
      ) : (
        children
      )}
    </Button>
  );
};

interface ProtectedLinkProps {
  children: ReactNode;
  to: string;
  requireAuth?: boolean;
  requireAdmin?: boolean;
  className?: string;
  fallback?: ReactNode;
}

/**
 * Компонент для защищенных ссылок
 */
export const ProtectedLink: React.FC<ProtectedLinkProps> = ({
  children,
  to,
  requireAuth = false,
  requireAdmin = false,
  className,
  fallback = null,
}) => {
  const { t } = useLanguage();
  const { isAuthenticated } = useAuth();
  const { isAdmin, isLoading } = useAdminAccess();

  const handleClick = (e: React.MouseEvent) => {
    if (requireAuth && !isAuthenticated) {
      e.preventDefault();
      toast.error(t('authRequired'), {
        description: t('pleaseSignInToContinue'),
      });
      return;
    }

    if (requireAdmin && !isAdmin) {
      e.preventDefault();
      toast.error(t('accessDenied'), {
        description: t('adminAccessRequired'),
      });
      return;
    }
  };

  // Проверяем права доступа
  if (requireAuth && !isAuthenticated) {
    return <>{fallback}</>;
  }

  if (requireAdmin && (isLoading || !isAdmin)) {
    return <>{fallback}</>;
  }

  return (
    <a href={to} onClick={handleClick} className={className}>
      {children}
    </a>
  );
};

interface ProtectedContentProps {
  children: ReactNode;
  requireAuth?: boolean;
  requireAdmin?: boolean;
  fallback?: ReactNode;
  showAccessDenied?: boolean;
}

/**
 * Компонент для условного отображения контента на основе прав доступа
 */
export const ProtectedContent: React.FC<ProtectedContentProps> = ({
  children,
  requireAuth = false,
  requireAdmin = false,
  fallback = null,
  showAccessDenied = false,
}) => {
  const { t } = useLanguage();
  const { isAuthenticated } = useAuth();
  const { isAdmin, isLoading } = useAdminAccess();

  if (isLoading) {
    return <>{fallback}</>;
  }

  if (requireAuth && !isAuthenticated) {
    if (showAccessDenied) {
      return (
        <div className="flex items-center justify-center p-8 text-center">
          <div className="space-y-4">
            <Shield className="h-12 w-12 text-muted-foreground mx-auto" />
            <div>
              <h3 className="text-lg font-semibold">{t('authRequired')}</h3>
              <p className="text-muted-foreground">{t('pleaseSignInToContinue')}</p>
            </div>
          </div>
        </div>
      );
    }
    return <>{fallback}</>;
  }

  if (requireAdmin && !isAdmin) {
    if (showAccessDenied) {
      return (
        <div className="flex items-center justify-center p-8 text-center">
          <div className="space-y-4">
            <AlertTriangle className="h-12 w-12 text-destructive mx-auto" />
            <div>
              <h3 className="text-lg font-semibold">{t('accessDenied')}</h3>
              <p className="text-muted-foreground">{t('adminAccessRequired')}</p>
            </div>
          </div>
        </div>
      );
    }
    return <>{fallback}</>;
  }

  return <>{children}</>;
};

export default ProtectedAction;
