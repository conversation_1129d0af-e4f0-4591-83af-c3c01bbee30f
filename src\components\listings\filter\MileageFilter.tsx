
import React from 'react';
import { useLanguage } from '@/hooks/useLanguage';
import { Input } from '@/components/ui/input';

interface MileageFilterProps {
  mileageMin: string;
  setMileageMin: (value: string) => void;
  mileageMax: string;
  setMileageMax: (value: string) => void;
}

const MileageFilter: React.FC<MileageFilterProps> = ({
  mileageMin,
  setMileageMin,
  mileageMax,
  setMileageMax
}) => {
  const { t } = useLanguage();

  return (
    <div className="space-y-3">
      <h3 className="text-sm font-medium">{t('listings.mileage')}</h3>
      <div className="flex gap-3">
        <Input
          placeholder={t('listings.mileageFrom')}
          type="number"
          value={mileageMin}
          onChange={(e) => setMileageMin(e.target.value)}
        />
        <Input
          placeholder={t('listings.mileageTo')}
          type="number"
          value={mileageMax}
          onChange={(e) => setMileageMax(e.target.value)}
        />
      </div>
    </div>
  );
};

export default MileageFilter;
