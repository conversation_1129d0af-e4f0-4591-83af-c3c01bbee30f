
import React, { useState } from 'react';
import { useLanguage } from '@/hooks/useLanguage';
import { toast } from 'sonner';
import UserSearchAndFilter from './users/UserSearchAndFilter';
import UserTable from './users/UserTable';
import EmptyUserState from './users/EmptyUserState';
import { mockUsers, UserData } from './users/UsersData';
import { filterUsersByStatus } from './dashboard/pending-listings/utils';
import AdminListingsPagination from './listings/AdminListingsPagination';
import { paginateUsers } from './utils';

const AdminUsers: React.FC = () => {
  const { t } = useLanguage();
  const [searchTerm, setSearchTerm] = useState('');
  const [roleFilter, setRoleFilter] = useState('all');
  const [statusFilter, setStatusFilter] = useState('all');
  const [vipFilter, setVipFilter] = useState('all');
  const [paidFilter, setPaidFilter] = useState('all');
  const [isLoading, setIsLoading] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 10;
  
  // Filter users based on search term and filters
  const filteredUsers = mockUsers.filter(user => {
    // Search filter
    const matchesSearch = !searchTerm || 
      user.username.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.fullName.toLowerCase().includes(searchTerm.toLowerCase());
    
    // Role filter
    const matchesRole = roleFilter === 'all' || user.role === roleFilter;
    
    // Status filter
    const matchesStatus = statusFilter === 'all' || user.status === statusFilter;
    
    // VIP filter
    const matchesVip = 
      vipFilter === 'all' || 
      (vipFilter === 'vip' && user.vipStatus) || 
      (vipFilter === 'non-vip' && !user.vipStatus);
    
    // Paid filter
    const matchesPaid = 
      paidFilter === 'all' || 
      (paidFilter === 'paid' && user.paidStatus) || 
      (paidFilter === 'non-paid' && !user.paidStatus);
    
    return matchesSearch && matchesRole && matchesStatus && matchesVip && matchesPaid;
  });
  
  // Paginate users
  const { currentItems: paginatedUsers, totalPages } = paginateUsers(
    filteredUsers,
    currentPage,
    itemsPerPage
  );
  
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    window.scrollTo(0, 0);
  };
  
  const handleRoleChange = (userId: string, newRole: string) => {
    toast.success(`User role updated to ${newRole}`);
  };
  
  const handleStatusChange = (userId: string, newStatus: string) => {
    toast.success(`User status updated to ${newStatus}`);
  };
  
  const handlePasswordReset = (userId: string) => {
    toast.success('Password reset email sent');
  };
  
  const handleVipStatusChange = (userId: string, newStatus: boolean) => {
    toast.success(newStatus 
      ? t('admin.vipStatusAdded') 
      : t('admin.vipStatusRemoved')
    );
  };
  
  const handlePaidStatusChange = (userId: string, newStatus: boolean) => {
    toast.success(t('admin.paidStatusUpdated'));
  };
  
  const refreshUsers = () => {
    setIsLoading(true);
    setTimeout(() => {
      toast.success(t('userListRefreshed'));
      setIsLoading(false);
    }, 1000);
  };
  
  const clearFilters = () => {
    setSearchTerm('');
    setRoleFilter('all');
    setStatusFilter('all');
    setVipFilter('all');
    setPaidFilter('all');
    setCurrentPage(1);
  };
  
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
  };
  
  return (
    <div className="space-y-6">
      <UserSearchAndFilter
        searchTerm={searchTerm}
        setSearchTerm={setSearchTerm}
        roleFilter={roleFilter}
        setRoleFilter={setRoleFilter}
        statusFilter={statusFilter}
        setStatusFilter={setStatusFilter}
        vipFilter={vipFilter}
        setVipFilter={setVipFilter}
        paidFilter={paidFilter}
        setPaidFilter={setPaidFilter}
        refreshUsers={refreshUsers}
        isLoading={isLoading}
      />
      
      {filteredUsers.length > 0 ? (
        <>
          <UserTable
            users={paginatedUsers}
            formatDate={formatDate}
            onRoleChange={handleRoleChange}
            onStatusChange={handleStatusChange}
            onPasswordReset={handlePasswordReset}
            onVipStatusChange={handleVipStatusChange}
            onPaidStatusChange={handlePaidStatusChange}
          />
          
          <AdminListingsPagination 
            currentPage={currentPage}
            totalPages={totalPages}
            onPageChange={handlePageChange}
          />
        </>
      ) : (
        <EmptyUserState onClearFilters={clearFilters} />
      )}
    </div>
  );
};

export default AdminUsers;
