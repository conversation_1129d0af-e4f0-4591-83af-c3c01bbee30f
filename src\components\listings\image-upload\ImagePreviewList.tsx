
import React from 'react';
import ImagePreview from './ImagePreview';

interface ImagePreviewListProps {
  previewUrls: string[];
  onRemoveImage: (index: number) => void;
}

const ImagePreviewList: React.FC<ImagePreviewListProps> = ({
  previewUrls,
  onRemoveImage
}) => {
  if (previewUrls.length === 0) {
    return null;
  }
  
  return (
    <div className="mt-3 flex flex-wrap gap-2">
      {previewUrls.map((url, index) => (
        <ImagePreview 
          key={index} 
          url={url} 
          index={index} 
          onRemove={onRemoveImage}
        />
      ))}
    </div>
  );
};

export default ImagePreviewList;
