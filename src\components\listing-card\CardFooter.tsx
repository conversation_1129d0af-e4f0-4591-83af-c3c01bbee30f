
import React from 'react';
import { MapPin, Calendar } from 'lucide-react';
import { useLanguage } from '@/hooks/useLanguage';
import { formatDistanceToNow } from 'date-fns';
import { ru, enUS } from 'date-fns/locale';
import i18next from '@/utils/i18n';

interface CardFooterProps {
  location: string;
  createdAt?: string;
}

const CardFooter: React.FC<CardFooterProps> = ({ location, createdAt }) => {
  const { language } = useLanguage();
  
  const formatDate = () => {
    if (!createdAt) return '2 days ago';
    
    let locale = enUS;
    if (language === 'ru') {
      locale = ru;
    }
    
    try {
      return formatDistanceToNow(new Date(createdAt), { 
        addSuffix: true,
        locale
      });
    } catch (error) {
      console.error('Date formatting error:', error);
      return '2 days ago';
    }
  };
  
  return (
    <div className="flex items-center justify-between text-sm text-muted-foreground">
      <div className="flex items-center">
        <MapPin className="h-4 w-4 mr-1" />
        <span>{location}</span>
      </div>
      <div className="flex items-center">
        <Calendar className="h-4 w-4 mr-1" />
        <span>{formatDate()}</span>
      </div>
    </div>
  );
};

export default CardFooter;
