
import React from 'react';
import { RefreshCcw } from 'lucide-react';
import { useLanguage } from '@/hooks/useLanguage';
import { useTranslation } from 'react-i18next';

const LoadingState: React.FC = () => {
  const { language } = useLanguage();
  const { t } = useTranslation();
  
  return (
    <div className="flex flex-col items-center justify-center h-64">
      <RefreshCcw className="h-8 w-8 animate-spin text-primary mb-4" />
      <p className="text-muted-foreground">
        {language === 'ru' ? 'Загрузка...' : t('loading')}
      </p>
    </div>
  );
};

export default LoadingState;
