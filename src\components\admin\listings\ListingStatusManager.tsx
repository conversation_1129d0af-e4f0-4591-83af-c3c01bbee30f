
import React from 'react';
import { useLanguage } from '@/hooks/useLanguage';
import { useTranslation } from 'react-i18next';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { BadgeCheck, BadgeDollarSign, Shield } from 'lucide-react';
import { Switch } from '@/components/ui/switch';
import { toast } from 'sonner';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardFooter, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card';

interface ListingStatusManagerProps {
  listingId: string;
  isVip: boolean;
  isPaid: boolean;
  onVipStatusChange: (id: string, status: boolean) => Promise<void>;
  onPaidStatusChange: (id: string, status: boolean) => Promise<void>;
}

const ListingStatusManager: React.FC<ListingStatusManagerProps> = ({
  listingId,
  isVip,
  isPaid,
  onVipStatusChange,
  onPaidStatusChange
}) => {
  const { language } = useLanguage();
  const { t } = useTranslation();
  const [vipLoading, setVipLoading] = React.useState(false);
  const [paidLoading, setPaidLoading] = React.useState(false);
  
  const handleVipStatusChange = async (checked: boolean) => {
    try {
      setVipLoading(true);
      await onVipStatusChange(listingId, checked);
      toast.success(
        checked ? t('admin.vipStatusAdded') : t('admin.vipStatusRemoved'),
        {
          description: checked ? t('admin.vipStatusAddedDesc') : t('admin.vipStatusRemovedDesc')
        }
      );
    } catch (error) {
      console.error('Error changing VIP status:', error);
      toast.error(t('admin.vipStatusUpdateError'), {
        description: t('admin.vipStatusUpdateErrorDesc')
      });
    } finally {
      setVipLoading(false);
    }
  };
  
  const handlePaidStatusChange = async (checked: boolean) => {
    try {
      setPaidLoading(true);
      await onPaidStatusChange(listingId, checked);
      toast.success(t('admin.paidStatusUpdated'), {
        description: t('admin.paidStatusUpdatedDesc')
      });
    } catch (error) {
      console.error('Error changing paid status:', error);
      toast.error(t('admin.paidStatusUpdateError'), {
        description: t('admin.paidStatusUpdateErrorDesc')
      });
    } finally {
      setPaidLoading(false);
    }
  };
  
  return (
    <Card>
      <CardHeader>
        <CardTitle>{language === 'ru' ? 'Статус объявления' : t('admin.listingStatus')}</CardTitle>
        <CardDescription>
          {language === 'ru' 
            ? 'Управление статусом VIP и оплаты объявления' 
            : 'Manage VIP status and payment status for this listing'}
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <BadgeCheck className="text-blue-500 h-5 w-5" />
            <span>{language === 'ru' ? 'VIP статус' : t('admin.vipStatus')}</span>
            {isVip && (
              <Badge variant="secondary" className="ml-2 bg-blue-100 text-blue-800">
                VIP
              </Badge>
            )}
          </div>
          <Switch 
            checked={isVip}
            onCheckedChange={handleVipStatusChange}
            disabled={vipLoading}
          />
        </div>
        
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <BadgeDollarSign className="text-green-500 h-5 w-5" />
            <span>{language === 'ru' ? 'Статус оплаты' : t('admin.paidStatus')}</span>
            {isPaid && (
              <Badge variant="secondary" className="ml-2 bg-green-100 text-green-800">
                {language === 'ru' ? 'Оплачено' : t('admin.paid')}
              </Badge>
            )}
          </div>
          <Switch 
            checked={isPaid}
            onCheckedChange={handlePaidStatusChange}
            disabled={paidLoading}
          />
        </div>
      </CardContent>
    </Card>
  );
};

export default ListingStatusManager;
