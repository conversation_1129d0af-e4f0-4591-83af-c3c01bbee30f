
import React from 'react';
import { useLanguage } from '@/hooks/useLanguage';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Zap } from 'lucide-react';
import { Button } from '@/components/ui/button';

const RecentActivity: React.FC = () => {
  const { t } = useLanguage();

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-lg font-medium">
          {t('admin.recentActivity')}
        </CardTitle>
        <Zap className="h-4 w-4 text-muted-foreground" />
      </CardHeader>
      <CardContent>
        <div className="mt-4 space-y-2">
          <div className="flex items-center justify-between border-b py-2">
            <div>
              <p className="text-sm font-medium">{t('admin.userRegistration')}</p>
              <p className="text-xs text-muted-foreground">John Doe - 1 {t('hourAgo')}</p>
            </div>
          </div>
          <div className="flex items-center justify-between border-b py-2">
            <div>
              <p className="text-sm font-medium">{t('admin.listingUpdated')}</p>
              <p className="text-xs text-muted-foreground">Toyota Camry Parts - 3 {t('hoursAgo')}</p>
            </div>
          </div>
          <div className="flex items-center justify-between border-b py-2">
            <div>
              <p className="text-sm font-medium">{t('admin.userLogin')}</p>
              <p className="text-xs text-muted-foreground">Jane Smith - 4 {t('hoursAgo')}</p>
            </div>
          </div>
        </div>
        <Button variant="link" size="sm" className="mt-4 p-0">
          {t('admin.viewAll')}
        </Button>
      </CardContent>
    </Card>
  );
};

export default RecentActivity;
