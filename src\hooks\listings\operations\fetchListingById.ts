
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';
import { Listing } from '../types';
import { useLanguage } from '@/hooks/useLanguage';

export const useFetchListingById = (setIsLoading: React.Dispatch<React.SetStateAction<boolean>>) => {
  const { t } = useLanguage();
  
  const fetchListingById = async (id: string): Promise<Listing | null> => {
    try {
      setIsLoading(true);
      console.log("Fetching listing with ID:", id);
      
      // Simplify the fetch operation to reduce chances of errors
      const { data, error } = await supabase
        .from('listings')
        .select('*')
        .eq('id', id)
        .single();
      
      if (error) {
        console.error('Error fetching listing:', error);
        toast.error(t('listings.failedToLoadListing'));
        throw error;
      }
      
      console.log("Fetched listing details:", data);
      return data as Listing;
    } catch (error) {
      console.error('Error fetching listing:', error);
      toast.error(t('listings.failedToLoadListing'));
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  return { fetchListingById };
};
