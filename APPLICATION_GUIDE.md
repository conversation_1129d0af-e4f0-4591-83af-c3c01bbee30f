# Руководство по использованию приложения "Авторынок запчастей"

## Запуск сервера разработки

1. Запустите сервер разработки с помощью команды:
   ```
   npm run dev
   ```

2. Обратите внимание на URL, который будет выведен в консоли, например:
   ```
   ➜  Local:   http://localhost:8084/
   ```

3. Используйте этот URL для доступа к приложению.

## Функциональность приложения

### Главная страница (/)
- Отображает героический блок с кнопками навигации
- Показывает последние объявления
- Кнопка "Объявления" ведет на страницу всех объявлений
- Кнопка "Местоположение" открывает диалог для поиска по городу
- Кнопка "Создать объявление" ведет на страницу создания объявления

### Страница объявлений (/listings)
- Отображает все доступные объявления
- Поддерживает поиск по названию, марке и модели
- Фильтрация по местоположению
- Переключение между видами сетки (многоколоночный/одноколоночный)
- Кнопка "Назад" для возврата на предыдущую страницу
- Поддерживает URL-параметры (например, ?location=Germany)

### Страница детального просмотра (/listings/:id)
- Отображает подробную информацию об объявлении
- Показывает характеристики и описание
- Информация о продавце с контактными данными
- Кнопки для звонка, сохранения, поделиться и жалобы
- Кнопка "Назад" для возврата на предыдущую страницу

### Другие страницы
- /auth - страница авторизации
- /profile - профиль пользователя
- /create-listing - создание объявления
- /messages - сообщения
- /admin - панель администратора

## Переводы

Приложение поддерживает три языка:
- Русский (по умолчанию)
- Английский
- Испанский

Переключение языка доступно в заголовке сайта.

## Тестовые данные

Приложение использует тестовые данные для демонстрации функциональности:
- На главной странице отображаются 4 тестовых объявления
- На странице объявлений доступно 6 тестовых объявлений
- Страница детального просмотра поддерживает объявления с ID 1 и 2

## Проблемы с портами

Если порт 8080 занят, Vite автоматически выберет другой свободный порт. Для освобождения порта:

- В Windows: `netstat -ano | findstr 8080` для поиска PID, затем `taskkill /F /PID <PID>`
- В Linux/Mac: `lsof -i :8080` для поиска PID, затем `kill -9 <PID>`

## Особенности реализации

- Все страницы имеют кнопку "Назад" для удобной навигации
- Поиск и фильтрация работают в реальном времени
- Адаптивный дизайн для мобильных устройств
- Темная и светлая темы
- Переводы на русский, английский и испанский языки
