
// Mock user data for demonstration
export const mockUsers = [
  {
    id: '1',
    username: 'john_doe',
    email: '<EMAIL>',
    fullName: '<PERSON>',
    role: 'user',
    status: 'active',
    registrationDate: '2023-01-15T12:30:00Z',
    lastLogin: '2023-05-20T15:45:00Z',
    vipStatus: true,
    paidStatus: true,
  },
  {
    id: '2',
    username: 'jane_smith',
    email: '<EMAIL>',
    fullName: '<PERSON>',
    role: 'admin',
    status: 'active',
    registrationDate: '2022-11-05T09:15:00Z',
    lastLogin: '2023-05-22T10:30:00Z',
    vipStatus: false,
    paidStatus: true,
  },
  {
    id: '3',
    username: 'bob_johnson',
    email: '<EMAIL>',
    fullName: '<PERSON>',
    role: 'user',
    status: 'inactive',
    registrationDate: '2023-02-20T14:00:00Z',
    lastLogin: '2023-04-10T11:20:00Z',
    vipStatus: false,
    paidStatus: false,
  },
  {
    id: '4',
    username: 'alice_wong',
    email: '<EMAIL>',
    fullName: '<PERSON>',
    role: 'moderator',
    status: 'active',
    registrationDate: '2022-12-10T08:45:00Z',
    lastLogin: '2023-05-21T16:15:00Z',
    vipStatus: true,
    paidStatus: false,
  },
  {
    id: '5',
    username: 'michael_brown',
    email: '<EMAIL>',
    fullName: 'Michael Brown',
    role: 'user',
    status: 'active',
    registrationDate: '2023-03-15T10:30:00Z',
    lastLogin: '2023-05-19T14:45:00Z',
    vipStatus: false,
    paidStatus: true,
  },
];

export interface UserData {
  id: string;
  username: string;
  email: string;
  fullName: string;
  role: string;
  status: string;
  registrationDate: string;
  lastLogin: string;
  vipStatus: boolean;
  paidStatus: boolean;
}
