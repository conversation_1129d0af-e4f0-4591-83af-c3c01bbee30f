
import React from 'react';
import { useLanguage } from '@/hooks/useLanguage';
import { Link } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { useAuth } from '@/contexts/AuthContext';

interface ListingsPageHeaderProps {
  totalCount: number;
  filteredCount: number;
  isLoading: boolean;
  currentPage: number;
  totalPages: number;
  onCreateTestListings: () => void;
}

const ListingsPageHeader: React.FC<ListingsPageHeaderProps> = ({
  totalCount,
  filteredCount,
  isLoading,
  currentPage,
  totalPages,
  onCreateTestListings
}) => {
  const { t } = useLanguage();
  const { isAuthenticated } = useAuth();

  return (
    <div className="mb-8 flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
      <div>
        <h1 className="text-2xl md:text-3xl font-bold mb-2">
          {t('listingsNav')}
        </h1>
        <p className="text-muted-foreground">
          {totalCount === 0 && !isLoading ? (
            <span>{t('noListingsFound')} {isAuthenticated && t('listings.canCreateOrAddTest')}</span>
          ) : (
            <span>
              {t('showing')} {filteredCount} {t('of')} {totalCount} {t('listingsLabel')}
              {currentPage > 1 && ` - ${t('page')} ${currentPage} ${t('of')} ${totalPages}`}
            </span>
          )}
        </p>
      </div>

      <div className="flex items-center gap-2">
        {isAuthenticated && totalCount === 0 && (
          <Button onClick={onCreateTestListings} variant="outline" size="sm">
            {t('listings.addTestListings')}
          </Button>
        )}

        {isAuthenticated && (
          <Button asChild size="sm">
            <Link to="/create-listing" className="flex items-center whitespace-nowrap">
              {t('listings.createListing')}
            </Link>
          </Button>
        )}
      </div>
    </div>
  );
};

export default ListingsPageHeader;
