
import React from 'react';
import { useLanguage } from '@/hooks/useLanguage';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

interface YearFilterProps {
  yearFrom: string;
  setYearFrom: (value: string) => void;
  yearTo: string;
  setYearTo: (value: string) => void;
}

const YearFilter: React.FC<YearFilterProps> = ({
  yearFrom,
  setYearFrom,
  yearTo,
  setYearTo
}) => {
  const { t } = useLanguage();

  // Create year options
  const currentYear = new Date().getFullYear();
  const yearOptions = Array.from({ length: 21 }, (_, i) => (currentYear - i).toString());

  return (
    <div className="space-y-3">
      <h3 className="text-sm font-medium">{t('listings.year')}</h3>
      <div className="flex gap-3">
        <Select value={yearFrom} onValueChange={setYearFrom}>
          <SelectTrigger>
            <SelectValue placeholder={t('listings.yearFrom')} />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="any_year_from">{t('listings.any')}</SelectItem>
            {yearOptions.map((year) => (
              <SelectItem key={year} value={year}>
                {year}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        <Select value={yearTo} onValueChange={setYearTo}>
          <SelectTrigger>
            <SelectValue placeholder={t('listings.yearTo')} />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="any_year_to">{t('listings.any')}</SelectItem>
            {yearOptions.map((year) => (
              <SelectItem key={year} value={year}>
                {year}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
    </div>
  );
};

export default YearFilter;
