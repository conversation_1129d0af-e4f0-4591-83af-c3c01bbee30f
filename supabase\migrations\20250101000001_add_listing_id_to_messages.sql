-- Add listing_id column to messages table to link messages to specific listings
-- This allows users to see which listing a conversation is about

-- Add listing_id column as optional foreign key
ALTER TABLE public.messages 
ADD COLUMN listing_id UUID REFERENCES public.listings(id) ON DELETE SET NULL;

-- Add index for better performance when filtering by listing
CREATE INDEX idx_messages_listing_id ON public.messages(listing_id);

-- Add index for better performance when querying conversations
CREATE INDEX idx_messages_conversation ON public.messages(sender_id, recipient_id);

-- Add comment to explain the listing_id field
COMMENT ON COLUMN public.messages.listing_id IS 'Optional reference to the listing this message is about. NULL for general conversations.';
