
import React from 'react';
import { useLanguage } from '@/hooks/useLanguage';
import { Button } from '@/components/ui/button';

interface FilterActionsProps {
  onReset: () => void;
  onApply: () => void;
}

const FilterActions: React.FC<FilterActionsProps> = ({ onReset, onApply }) => {
  const { t } = useLanguage();

  return (
    <div className="pt-6 flex justify-between">
      <Button variant="outline" onClick={onReset}>{t('listings.reset')}</Button>
      <Button onClick={onApply}>{t('listings.applyFilters')}</Button>
    </div>
  );
};

export default FilterActions;
