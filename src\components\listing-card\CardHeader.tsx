
import React from 'react';
import { Link } from 'react-router-dom';

interface CardHeaderProps {
  title: string;
  listingUrl: string;
}

const CardHeader: React.FC<CardHeaderProps> = ({ title, listingUrl }) => {
  return (
    <Link to={listingUrl} className="hover:underline">
      <h3 className="font-medium text-lg tracking-tight line-clamp-1">{title}</h3>
    </Link>
  );
};

export default CardHeader;
