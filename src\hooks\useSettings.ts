
import { useState, useEffect } from 'react';
import { SiteSettings, loadSettings, saveSettings } from '@/services/settingsService';

export const useSettings = () => {
  const [settings, setSettings] = useState<SiteSettings>(loadSettings());
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  
  const fetchSettings = async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      // Load settings from localStorage via our service
      const loadedSettings = loadSettings();
      setSettings(loadedSettings);
    } catch (err) {
      console.error('Error fetching settings:', err);
      setError(err instanceof Error ? err : new Error('Unknown error fetching settings'));
    } finally {
      setIsLoading(false);
    }
  };
  
  const updateSettings = async (category: keyof SiteSettings, data: any) => {
    try {
      // Save settings to localStorage via our service
      await saveSettings(category, data);
      
      // Update state with the new settings
      setSettings(prev => ({
        ...prev,
        [category]: {
          ...prev[category],
          ...data
        }
      }));
      
      return true;
    } catch (err) {
      console.error(`Error updating ${category} settings:`, err);
      throw err;
    }
  };
  
  // Initial load
  useEffect(() => {
    fetchSettings();
  }, []);
  
  return { settings, isLoading, error, updateSettings };
};
