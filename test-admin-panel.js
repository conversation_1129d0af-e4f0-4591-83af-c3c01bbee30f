// Test script to check admin panel functionality
// Run this in browser console on admin page

console.log('=== ADMIN PANEL DIAGNOSTIC TEST ===');

// Test 1: Check if listings data is being fetched
console.log('1. Testing listings data fetch...');

// Check if useAdminListingsPagination hook is working
const checkPaginationHook = () => {
  console.log('Checking pagination hook state...');
  // This will be visible in React DevTools
};

// Test 2: Check Supabase connection
const testSupabaseConnection = async () => {
  console.log('2. Testing Supabase connection...');
  
  try {
    // Import supabase client
    const { supabase } = await import('./src/integrations/supabase/client.js');
    
    // Test basic query
    const { data, error, count } = await supabase
      .from('listings')
      .select('*', { count: 'exact' })
      .limit(5);
    
    if (error) {
      console.error('Supabase error:', error);
      return false;
    }
    
    console.log('✅ Supabase connection successful');
    console.log(`Found ${count} total listings`);
    console.log('Sample data:', data);
    return true;
  } catch (error) {
    console.error('❌ Supabase connection failed:', error);
    return false;
  }
};

// Test 3: Check pending listings
const testPendingListings = async () => {
  console.log('3. Testing pending listings...');
  
  try {
    const { supabase } = await import('./src/integrations/supabase/client.js');
    
    const { data, error } = await supabase
      .from('listings')
      .select('*')
      .eq('status', 'pending');
    
    if (error) {
      console.error('Error fetching pending listings:', error);
      return false;
    }
    
    console.log(`✅ Found ${data.length} pending listings`);
    console.log('Pending listings:', data);
    return true;
  } catch (error) {
    console.error('❌ Error testing pending listings:', error);
    return false;
  }
};

// Test 4: Test status update
const testStatusUpdate = async (listingId) => {
  console.log('4. Testing status update...');
  
  if (!listingId) {
    console.log('⚠️ No listing ID provided for status update test');
    return false;
  }
  
  try {
    const { supabase } = await import('./src/integrations/supabase/client.js');
    
    // Update status to approved
    const { error } = await supabase
      .from('listings')
      .update({ status: 'approved' })
      .eq('id', listingId);
    
    if (error) {
      console.error('Error updating status:', error);
      return false;
    }
    
    console.log(`✅ Successfully updated listing ${listingId} status to approved`);
    return true;
  } catch (error) {
    console.error('❌ Error testing status update:', error);
    return false;
  }
};

// Run all tests
const runAllTests = async () => {
  console.log('Starting comprehensive admin panel tests...\n');
  
  const results = {
    supabase: await testSupabaseConnection(),
    pending: await testPendingListings(),
  };
  
  console.log('\n=== TEST RESULTS ===');
  console.log('Supabase Connection:', results.supabase ? '✅ PASS' : '❌ FAIL');
  console.log('Pending Listings:', results.pending ? '✅ PASS' : '❌ FAIL');
  
  const allPassed = Object.values(results).every(result => result);
  console.log('\nOverall Status:', allPassed ? '✅ ALL TESTS PASSED' : '❌ SOME TESTS FAILED');
  
  if (!allPassed) {
    console.log('\n🔧 TROUBLESHOOTING STEPS:');
    if (!results.supabase) {
      console.log('- Check Supabase configuration');
      console.log('- Verify environment variables');
      console.log('- Check network connectivity');
    }
    if (!results.pending) {
      console.log('- Check if listings table has status column');
      console.log('- Verify pending listings exist in database');
    }
  }
  
  return results;
};

// Export functions for manual testing
window.adminPanelTest = {
  runAllTests,
  testSupabaseConnection,
  testPendingListings,
  testStatusUpdate
};

console.log('Test functions available as window.adminPanelTest');
console.log('Run window.adminPanelTest.runAllTests() to start testing');
