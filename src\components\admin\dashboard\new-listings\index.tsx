
import React, { useState } from 'react';
import { useLanguage } from '@/hooks/useLanguage';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Search } from 'lucide-react';
import { Listing } from '@/hooks/listings/types';
import ListingItem from './ListingItem';
import ListingPagination from './ListingPagination';
import EmptyState from './EmptyState';
import { paginateListings } from './utils';

interface NewListingsProps {
  listings: Listing[];
  onApprove: (id: string) => Promise<void>;
  isLoading: boolean;
}

const NewListings: React.FC<NewListingsProps> = ({ listings, onApprove, isLoading }) => {
  const { t } = useLanguage();
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 3;
  
  // Calculate pagination
  const { currentItems, totalPages } = paginateListings(listings, currentPage, itemsPerPage);
  
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };
  
  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-lg font-medium">
          {t('admin.newListings')}
        </CardTitle>
        <Search className="h-4 w-4 text-muted-foreground" />
      </CardHeader>
      <CardContent>
        {listings.length > 0 ? (
          <div className="mt-4 space-y-2">
            {currentItems.map(listing => (
              <ListingItem
                key={listing.id}
                id={listing.id}
                title={listing.title}
                createdAt={listing.created_at}
                status={listing.status || ''}
                onApprove={onApprove}
                isLoading={isLoading}
              />
            ))}
          </div>
        ) : (
          <EmptyState />
        )}
        
        <ListingPagination
          currentPage={currentPage}
          totalPages={totalPages}
          onPageChange={handlePageChange}
        />
      </CardContent>
    </Card>
  );
};

export default NewListings;
