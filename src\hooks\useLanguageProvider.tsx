
import { useState, useEffect, ReactNode } from 'react';
import i18next from '@/utils/i18n';
import { Language } from './useLanguage';

/**
 * Hook that provides language-related functionality for the LanguageProvider
 */
export const useLanguageProvider = () => {
  // Set default language to English or saved language
  const [language, setLanguage] = useState<Language>(() => {
    const savedLang = localStorage.getItem('i18nextLng');
    return (savedLang === 'ru') ? 'ru' : 'en';
  });

  useEffect(() => {
    // Ensure i18next is using the correct language on mount and when language changes
    if (i18next.isInitialized && i18next.language !== language) {
      i18next.changeLanguage(language);
    }

    // Handle language changes from other sources (like browser language detector)
    const handleI18NextLanguageChanged = () => {
      const currentLang = i18next.language;
      if (currentLang !== language && (currentLang === 'en' || currentLang === 'ru')) {
        setLanguage(currentLang as Language);
      }
    };

    i18next.on('languageChanged', handleI18NextLanguageChanged);

    return () => {
      i18next.off('languageChanged', handleI18NextLanguageChanged);
    };
  }, [language]);

  const changeLanguage = (lang: Language) => {
    setLanguage(lang);
    localStorage.setItem('i18nextLng', lang);

    // Ensure i18next language is also changed
    if (i18next.isInitialized) {
      i18next.changeLanguage(lang);
    }

    // Force a re-render of components using translations
    window.dispatchEvent(new Event('languageChanged'));

    console.log('Language changed to:', lang);
  };

  // Translation function that directly uses i18next
  const translate = (key: string): string => {
    if (!key) return '';

    // Use i18next for translation
    if (i18next.isInitialized) {
      // First try without forcing language to let i18next handle it
      const result = i18next.t(key);

      // If that fails and we get the key back, try with explicit language
      if (result === key) {
        return i18next.t(key, { lng: language });
      }

      return result;
    }

    // Fallback: return the key itself if i18next is not ready
    return key;
  };

  return {
    language,
    setLanguage: changeLanguage,
    t: translate
  };
};

export type LanguageProviderProps = {
  children: ReactNode;
};
