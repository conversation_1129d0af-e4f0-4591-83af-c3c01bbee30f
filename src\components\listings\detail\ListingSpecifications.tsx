
import React from 'react';
import { useLanguage } from '@/hooks/useLanguage';

interface ListingSpecificationsProps {
  make: string;
  model: string;
  year: number;
  location: string;
}

const ListingSpecifications: React.FC<ListingSpecificationsProps> = ({
  make,
  model,
  year,
  location
}) => {
  const { t } = useLanguage();

  // Get translated text immediately to ensure proper rendering
  const specificationsTitle = t('listings.specifications');
  const makeLabel = t('listings.make');
  const modelLabel = t('listings.model');
  const yearLabel = t('listings.year');
  const locationLabel = t('listings.location');

  return (
    <div className="space-y-4">
      <h2 className="text-xl font-bold">{specificationsTitle}</h2>
      <div className="grid grid-cols-2 gap-4">
        <div>
          <div className="text-sm text-muted-foreground">{makeLabel}</div>
          <div className="font-medium">{make}</div>
        </div>
        <div>
          <div className="text-sm text-muted-foreground">{modelLabel}</div>
          <div className="font-medium">{model}</div>
        </div>
        <div>
          <div className="text-sm text-muted-foreground">{yearLabel}</div>
          <div className="font-medium">{year}</div>
        </div>
        <div>
          <div className="text-sm text-muted-foreground">{locationLabel}</div>
          <div className="font-medium">{location}</div>
        </div>
      </div>
    </div>
  );
};

export default ListingSpecifications;
