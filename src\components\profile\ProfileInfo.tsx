
import React from 'react';
import { useProfileUpdate, Profile } from '@/hooks/useProfileUpdate';
import ProfileAvatar from './ProfileAvatar';
import ProfileForm from './ProfileForm';

interface ProfileInfoProps {
  profile: Profile;
  userEmail: string | undefined;
  onProfileUpdate: (updatedProfile: Profile) => void;
}

const ProfileInfo: React.FC<ProfileInfoProps> = ({ 
  profile, 
  userEmail, 
  onProfileUpdate 
}) => {
  const {
    username,
    setUsername,
    fullName,
    setFullName,
    phone,
    setPhone,
    avatarUrl,
    isSaving,
    handleAvatarChange,
    handleSaveProfile
  } = useProfileUpdate(profile, onProfileUpdate);

  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
      <ProfileAvatar
        avatarUrl={avatarUrl}
        fullName={fullName}
        username={username}
        userEmail={userEmail}
        onAvatarChange={handleAvatarChange}
      />
      
      <ProfileForm
        username={username}
        fullName={fullName}
        phone={phone}
        userEmail={userEmail}
        isSaving={isSaving}
        onUsernameChange={(e) => setUsername(e.target.value)}
        onFullNameChange={(e) => setFullName(e.target.value)}
        onPhoneChange={(e) => setPhone(e.target.value)}
        onSave={handleSaveProfile}
      />
    </div>
  );
};

export default ProfileInfo;
