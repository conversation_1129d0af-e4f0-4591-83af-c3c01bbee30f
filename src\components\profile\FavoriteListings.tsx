import React from 'react';
import { Button } from '@/components/ui/button';
import { useLanguage } from '@/hooks/useLanguage';
import ListingCard from '@/components/ListingCard';
import { Listing } from '@/hooks/listings/types';

interface FavoriteListingsProps {
  listings: Listing[];
}

const FavoriteListings: React.FC<FavoriteListingsProps> = ({ listings }) => {
  const { t } = useLanguage();

  return (
    <div>
      <div className="mb-4">
        <h2 className="text-xl font-semibold">{t('profile.favorites')}</h2>
      </div>

      {listings.length > 0 ? (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
          {listings.map((listing) => (
            <ListingCard
              key={listing.id}
              id={listing.id}
              title={listing.title}
              year={listing.year}
              make={listing.make}
              model={listing.model}
              price={Number(listing.price)}
              location={listing.location}
              imageUrl={listing.image_urls[0] || 'https://images.unsplash.com/photo-1533473359331-0135ef1b58bf?auto=format&fit=crop&q=80&w=2670&ixlib=rb-4.0.3'}
              featured={listing.featured}
              createdAt={listing.created_at}
            />
          ))}
        </div>
      ) : (
        <div className="text-center py-20 bg-muted/20 rounded-lg">
          <p className="text-muted-foreground mb-4">{t('profile.noFavoritesYet')}</p>
          <Button asChild>
            <a href="/listings">{t('listings.exploreListings')}</a>
          </Button>
        </div>
      )}
    </div>
  );
};

export default FavoriteListings;
