import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { supabase } from '@/integrations/supabase/client';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { toast } from 'sonner';
import { Bug, Database, User, Shield } from 'lucide-react';
import Header from '@/components/Header';
import Footer from '@/components/Footer';

/**
 * Страница для отладки проблем с правами администратора
 */
const DebugAdmin: React.FC = () => {
  const { user, isAuthenticated } = useAuth();
  const [debugInfo, setDebugInfo] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);

  const checkDatabaseStructure = async () => {
    setIsLoading(true);
    const info: any = {
      user: null,
      profile: null,
      tables: null,
      errors: []
    };

    try {
      // Проверяем текущего пользователя
      const { data: userData, error: userError } = await supabase.auth.getUser();
      if (userError) {
        info.errors.push(`User error: ${userError.message}`);
      } else {
        info.user = userData.user;
      }

      // Проверяем существование таблицы profiles
      try {
        const { data: profileData, error: profileError } = await supabase
          .from('profiles')
          .select('*')
          .limit(1);
        
        if (profileError) {
          info.errors.push(`Profiles table error: ${profileError.message}`);
        } else {
          info.tables = { profiles: 'exists' };
        }
      } catch (error) {
        info.errors.push(`Profiles table check failed: ${error}`);
      }

      // Проверяем профиль текущего пользователя
      if (user) {
        try {
          const { data: currentProfile, error: currentProfileError } = await supabase
            .from('profiles')
            .select('*')
            .eq('id', user.id)
            .single();
          
          if (currentProfileError) {
            info.errors.push(`Current profile error: ${currentProfileError.message}`);
          } else {
            info.profile = currentProfile;
          }
        } catch (error) {
          info.errors.push(`Current profile check failed: ${error}`);
        }
      }

      setDebugInfo(info);
    } catch (error) {
      info.errors.push(`General error: ${error}`);
      setDebugInfo(info);
    } finally {
      setIsLoading(false);
    }
  };

  const createProfile = async () => {
    if (!user) {
      toast.error('Пользователь не авторизован');
      return;
    }

    setIsLoading(true);
    try {
      // Создаем профиль с минимальными данными
      const { data, error } = await supabase
        .from('profiles')
        .insert({
          id: user.id,
          username: user.email?.split('@')[0] || 'user',
          full_name: user.user_metadata?.full_name || user.email || 'User',
          is_admin: user.email === '<EMAIL>',
          role: user.email === '<EMAIL>' ? 'admin' : 'user'
        })
        .select()
        .single();

      if (error) {
        throw error;
      }

      toast.success('Профиль создан успешно!');
      checkDatabaseStructure(); // Обновляем информацию
    } catch (error) {
      console.error('Error creating profile:', error);
      toast.error('Ошибка при создании профиля', {
        description: error instanceof Error ? error.message : 'Неизвестная ошибка'
      });
    } finally {
      setIsLoading(false);
    }
  };

  const updateToAdmin = async () => {
    if (!user) {
      toast.error('Пользователь не авторизован');
      return;
    }

    setIsLoading(true);
    try {
      // Обновляем существующий профиль
      const { data, error } = await supabase
        .from('profiles')
        .update({
          is_admin: true,
          role: 'admin'
        })
        .eq('id', user.id)
        .select()
        .single();

      if (error) {
        throw error;
      }

      toast.success('Права администратора назначены!');
      checkDatabaseStructure(); // Обновляем информацию
    } catch (error) {
      console.error('Error updating to admin:', error);
      toast.error('Ошибка при обновлении прав', {
        description: error instanceof Error ? error.message : 'Неизвестная ошибка'
      });
    } finally {
      setIsLoading(false);
    }
  };

  const upsertProfile = async () => {
    if (!user) {
      toast.error('Пользователь не авторизован');
      return;
    }

    setIsLoading(true);
    try {
      // Используем upsert для создания или обновления
      const { data, error } = await supabase
        .from('profiles')
        .upsert({
          id: user.id,
          username: user.email?.split('@')[0] || 'user',
          full_name: user.user_metadata?.full_name || user.email || 'User',
          is_admin: true,
          role: 'admin',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .select()
        .single();

      if (error) {
        throw error;
      }

      toast.success('Профиль создан/обновлен с правами администратора!');
      checkDatabaseStructure(); // Обновляем информацию
    } catch (error) {
      console.error('Error upserting profile:', error);
      toast.error('Ошибка при создании/обновлении профиля', {
        description: error instanceof Error ? error.message : 'Неизвестная ошибка'
      });
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (isAuthenticated) {
      checkDatabaseStructure();
    }
  }, [isAuthenticated]);

  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      
      <main className="flex-grow container mx-auto py-8 px-4">
        <div className="max-w-4xl mx-auto space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Bug className="h-5 w-5" />
                Отладка прав администратора
              </CardTitle>
              <CardDescription>
                Диагностика и исправление проблем с правами администратора
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Button onClick={checkDatabaseStructure} disabled={isLoading}>
                {isLoading ? 'Проверка...' : 'Обновить информацию'}
              </Button>
            </CardContent>
          </Card>

          {debugInfo && (
            <>
              {/* Информация о пользователе */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <User className="h-5 w-5" />
                    Информация о пользователе
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <pre className="bg-muted p-4 rounded text-sm overflow-auto">
                    {JSON.stringify(debugInfo.user, null, 2)}
                  </pre>
                </CardContent>
              </Card>

              {/* Информация о профиле */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Database className="h-5 w-5" />
                    Профиль в базе данных
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {debugInfo.profile ? (
                    <pre className="bg-muted p-4 rounded text-sm overflow-auto">
                      {JSON.stringify(debugInfo.profile, null, 2)}
                    </pre>
                  ) : (
                    <div className="p-4 bg-yellow-50 border border-yellow-200 rounded">
                      <p className="text-yellow-800">Профиль не найден в базе данных</p>
                    </div>
                  )}
                  
                  <div className="flex gap-2 flex-wrap">
                    <Button onClick={createProfile} disabled={isLoading} variant="outline">
                      Создать профиль
                    </Button>
                    <Button onClick={updateToAdmin} disabled={isLoading} variant="outline">
                      Обновить до админа
                    </Button>
                    <Button onClick={upsertProfile} disabled={isLoading}>
                      Создать/Обновить профиль (Upsert)
                    </Button>
                  </div>
                </CardContent>
              </Card>

              {/* Ошибки */}
              {debugInfo.errors.length > 0 && (
                <Card className="border-red-200">
                  <CardHeader>
                    <CardTitle className="text-red-800">Ошибки</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <ul className="space-y-2">
                      {debugInfo.errors.map((error: string, index: number) => (
                        <li key={index} className="text-red-700 text-sm">
                          • {error}
                        </li>
                      ))}
                    </ul>
                  </CardContent>
                </Card>
              )}
            </>
          )}

          {!isAuthenticated && (
            <Card className="border-yellow-200 bg-yellow-50">
              <CardContent className="pt-6">
                <p className="text-yellow-800">
                  Войдите в систему для диагностики прав администратора
                </p>
              </CardContent>
            </Card>
          )}
        </div>
      </main>
      
      <Footer />
    </div>
  );
};

export default DebugAdmin;
