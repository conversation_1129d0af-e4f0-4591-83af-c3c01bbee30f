
import React from 'react';
import { useLanguage } from '@/hooks/useLanguage';
import { Button } from '@/components/ui/button';
import { useNavigate } from 'react-router-dom';
import { Plus } from 'lucide-react';
import SearchBar from './SearchBar';
import { useAuth } from '@/contexts/AuthContext';

interface ListingsHeaderProps {
  totalCount: number;
  searchValue: string;
  onSearch: (value: string) => void;
  onToggleFilters: () => void;
  showFilters: boolean;
}

const ListingsHeader: React.FC<ListingsHeaderProps> = ({
  totalCount,
  searchValue,
  onSearch,
  onToggleFilters,
  showFilters,
}) => {
  const { t } = useLanguage();
  const navigate = useNavigate();
  const { isAuthenticated } = useAuth();

  // Pre-translate strings to ensure proper rendering
  const listingsTitle = t('listingsNav');
  const showingText = t('listings.showing');
  const ofText = t('listings.of');
  const listingsLabelText = t('listings.listingsLabel');
  const createListingText = t('listings.createListing');
  const hideFiltersText = t('listings.hideFilters');
  const filterText = t('ui.filter');

  return (
    <div className="flex flex-col gap-4 mb-6">
      <div className="flex flex-col sm:flex-row justify-between gap-4 items-start sm:items-center">
        <div>
          <h1 className="text-2xl md:text-3xl font-bold">{listingsTitle}</h1>
          {totalCount > 0 && (
            <p className="text-muted-foreground mt-1">
              {showingText} {totalCount} {ofText} {totalCount} {listingsLabelText}
            </p>
          )}
        </div>

        <Button
          onClick={() => navigate('/create-listing')}
          disabled={!isAuthenticated}
          className="ml-auto"
        >
          <Plus className="mr-2 h-4 w-4" />
          {createListingText}
        </Button>
      </div>

      <div className="flex flex-col sm:flex-row gap-4">
        <SearchBar
          searchTerm={searchValue}
          onSearchChange={onSearch}
          className="flex-1"
        />

        <Button
          variant="outline"
          onClick={onToggleFilters}
          className="sm:w-auto w-full"
        >
          {showFilters ? hideFiltersText : filterText}
        </Button>
      </div>
    </div>
  );
};

export default ListingsHeader;
