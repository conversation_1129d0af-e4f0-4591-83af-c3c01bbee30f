
import React, { useCallback, useEffect } from 'react';
import { useLanguage } from '@/hooks/useLanguage';
import { Tabs } from '@/components/ui/tabs';
import AdminAuth from '@/components/admin/AdminAuth';
import AdminPageLayout from '@/components/admin/layout/AdminPageLayout';
import AdminTabsNav from '@/components/admin/dashboard/AdminTabsNav';
import AdminTabsContent from '@/components/admin/dashboard/AdminTabsContent';
import { useAdminTabs } from '@/hooks/admin/useAdminTabs';
import { useLanguageChangeEffect } from '@/hooks/admin/useLanguageChangeEffect';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import BackButton from '@/components/BackButton';

// Create a client
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      refetchOnWindowFocus: false,
      staleTime: 1000 * 60 * 5, // 5 minutes
    },
  },
});

const AdminDashboard: React.FC = () => {
  const { t } = useLanguage();
  const { activeTab, setActiveTab } = useAdminTabs('overview');
  
  // Get translated title first
  const adminTitle = t('admin.title');
  
  // Force re-render on language change to ensure correct tab display
  const handleLanguageChange = useCallback(() => {
    console.log('Handling language change in AdminDashboard');
    // Force re-render by setting state with a callback to ensure we don't lose the current tab
    setActiveTab(prevTab => prevTab);
  }, [setActiveTab]);
  
  // Register language change listener
  useLanguageChangeEffect(handleLanguageChange);
  
  // Debug check for translations
  useEffect(() => {
    console.log('Admin dashboard title translation:', adminTitle);
  }, [adminTitle]);
  
  return (
    <QueryClientProvider client={queryClient}>
      <AdminAuth>
        <div className="container mx-auto py-6">
          <BackButton to="/" className="mb-6" />
          <AdminPageLayout title={adminTitle}>
            <Tabs 
              defaultValue="overview" 
              value={activeTab} 
              onValueChange={setActiveTab} 
              className="space-y-6"
            >
              <AdminTabsNav activeTab={activeTab} />
              <AdminTabsContent />
            </Tabs>
          </AdminPageLayout>
        </div>
      </AdminAuth>
    </QueryClientProvider>
  );
};

export default AdminDashboard;
