
import { useState, useEffect, useCallback } from "react";
import { supabase } from '@/integrations/supabase/client';
import { useToast } from "@/components/ui/use-toast";
import { useAuth } from "@/contexts/AuthContext";
import { toast } from "sonner";
import { Listing, UseListingsReturn } from "./listings/types";
import { useListingsOperations } from "./listings/useListingsOperations";
import { useTestListings } from "./listings/useTestListings";

export * from './listings/types';

export const useListings = (): UseListingsReturn => {
  const [listings, setListings] = useState<Listing[]>([]);
  const [featuredListings, setFeaturedListings] = useState<Listing[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  const { toast: uiToast } = useToast();
  const { user } = useAuth();

  // Fetch a single listing by ID
  const fetchListingById = async (id: string): Promise<Listing | null> => {
    try {
      setIsLoading(true);
      console.log(`Fetching listing with ID: ${id}`);

      const { data, error } = await supabase
        .from('listings')
        .select('*')
        .eq('id', id)
        .single();

      if (error) {
        console.error('Error fetching listing:', error);
        throw error;
      }

      console.log('Fetched listing:', data);
      return data as Listing;
    } catch (error) {
      console.error(`Error fetching listing with ID ${id}:`, error);
      setError(error as Error);
      uiToast({
        title: "Error loading",
        description: `Failed to load listing with ID ${id}`,
        variant: "destructive"
      });
      return null;
    } finally {
      setIsLoading(false);
    }
  };

  const fetchListings = useCallback(async () => {
    try {
      setIsLoading(true);
      console.log("Fetching all listings...");

      // Get unique listings by ID to prevent duplicates
      const { data, error } = await supabase
        .from('listings')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) {
        throw error;
      }

      console.log("Fetched listings:", data);

      // Filter out duplicates by ID
      const uniqueListings = data ?
        Array.from(new Map(data.map(item => [item.id, item])).values()) :
        [];

      setListings(uniqueListings as Listing[]);
    } catch (error) {
      console.error('Error fetching listings:', error);
      setError(error as Error);
      uiToast({
        title: "Error loading",
        description: "Failed to load listings",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  }, [uiToast]);

  // New function for paginated listings (for admin panel)
  const fetchPaginatedListings = useCallback(async (
    page: number = 1,
    limit: number = 50,
    searchTerm?: string,
    filters?: Record<string, any>
  ) => {
    try {
      setIsLoading(true);
      console.log(`Fetching paginated listings: page ${page}, limit ${limit}`);

      const offset = (page - 1) * limit;

      let query = supabase
        .from('listings')
        .select('*', { count: 'exact' })
        .order('created_at', { ascending: false })
        .range(offset, offset + limit - 1);

      // Apply search filter
      if (searchTerm && searchTerm.trim()) {
        const searchLower = searchTerm.toLowerCase();
        query = query.or(`title.ilike.%${searchLower}%,make.ilike.%${searchLower}%,model.ilike.%${searchLower}%,location.ilike.%${searchLower}%`);
      }

      // Apply additional filters
      if (filters) {
        if (filters.featured !== 'all') {
          query = query.eq('featured', filters.featured === 'featured');
        }
        if (filters.vipStatus !== 'all') {
          query = query.eq('vip_status', filters.vipStatus === 'vip');
        }
        if (filters.paidStatus !== 'all') {
          query = query.eq('paid_status', filters.paidStatus === 'paid');
        }
        if (filters.status && filters.status !== 'all') {
          query = query.eq('status', filters.status);
        }
      }

      const { data, error, count } = await query;

      if (error) {
        throw error;
      }

      console.log("Fetched paginated listings:", data, "Total count:", count);

      return {
        listings: data as Listing[],
        totalCount: count || 0,
        totalPages: Math.ceil((count || 0) / limit),
        currentPage: page,
        itemsPerPage: limit
      };
    } catch (error) {
      console.error('Error fetching paginated listings:', error);
      setError(error as Error);
      uiToast({
        title: "Error loading",
        description: "Failed to load listings",
        variant: "destructive"
      });
      return {
        listings: [],
        totalCount: 0,
        totalPages: 0,
        currentPage: 1,
        itemsPerPage: limit
      };
    } finally {
      setIsLoading(false);
    }
  }, [uiToast]);

  // Function to fetch pending listings
  const fetchPendingListings = useCallback(async () => {
    try {
      console.log("Fetching pending listings...");

      const { data, error } = await supabase
        .from('listings')
        .select('*')
        .eq('status', 'pending')
        .order('created_at', { ascending: false });

      if (error) {
        throw error;
      }

      console.log("Fetched pending listings:", data);
      return data as Listing[];
    } catch (error) {
      console.error('Error fetching pending listings:', error);
      uiToast({
        title: "Error loading",
        description: "Failed to load pending listings",
        variant: "destructive"
      });
      return [];
    }
  }, [uiToast]);

  const fetchListingsOriginal = useCallback(async () => {
    try {
      setIsLoading(true);
      console.log("Fetching all listings...");

      // Get unique listings by ID to prevent duplicates
      const { data, error } = await supabase
        .from('listings')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) {
        throw error;
      }

      console.log("Fetched listings:", data);

      // Filter out duplicates by ID
      const uniqueListings = data ?
        Array.from(new Map(data.map(item => [item.id, item])).values()) :
        [];

      setListings(uniqueListings as Listing[]);

      const { data: featuredData, error: featuredError } = await supabase
        .from('listings')
        .select('*')
        .eq('featured', true)
        .order('created_at', { ascending: false })
        .limit(4);

      if (featuredError) {
        throw featuredError;
      }

      console.log("Fetched featured listings:", featuredData);

      // Filter out duplicates for featured listings too
      const uniqueFeaturedListings = featuredData ?
        Array.from(new Map(featuredData.map(item => [item.id, item])).values()) :
        [];

      setFeaturedListings(uniqueFeaturedListings as Listing[]);
    } catch (error) {
      console.error('Error fetching listings:', error);
      setError(error as Error);
      uiToast({
        title: "Error loading",
        description: "Failed to load listings",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  }, [uiToast]);

  const operations = useListingsOperations(setIsLoading, setListings, setFeaturedListings, fetchListings);
  const { createTestListings } = useTestListings(fetchListings);

  // Ensure the operation functions match the expected return types
  const featureListingWrapper = async (id: string, featured: boolean): Promise<void> => {
    await operations.featureListing(id, featured);
  };

  const setVipStatusWrapper = async (id: string, vipStatus: boolean): Promise<void> => {
    await operations.setVipStatus(id, vipStatus);
  };

  const setPaidStatusWrapper = async (id: string, paidStatus: boolean): Promise<void> => {
    await operations.setPaidStatus(id, paidStatus);
  };

  const updateListingStatusWrapper = async (id: string, status: 'pending' | 'approved' | 'rejected'): Promise<void> => {
    await operations.updateListingStatus(id, status);
  };

  const deleteListingWrapper = async (id: string): Promise<void> => {
    await operations.deleteListing(id);
  };

  // Fetch listings on component mount
  useEffect(() => {
    fetchListings();
  }, [fetchListings]);

  return {
    listings,
    featuredListings,
    isLoading,
    error,
    fetchListings,
    fetchPaginatedListings,
    fetchPendingListings,
    fetchListingById,
    createListing: operations.createListing,
    updateListing: operations.updateListing,
    deleteListing: deleteListingWrapper,
    featureListing: featureListingWrapper,
    setVipStatus: setVipStatusWrapper,
    setPaidStatus: setPaidStatusWrapper,
    updateListingStatus: updateListingStatusWrapper,
    createTestListings
  };
};
