
import * as React from "react";
import { Check, ChevronsUpDown } from "lucide-react";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { useLanguage } from "@/hooks/useLanguage";

interface ComboboxProps {
  options: string[];
  value: string;
  setValue: (value: string) => void;
  placeholder?: string;
  className?: string;
  allowCustomValue?: boolean;
  onCustomValueChange?: (value: string) => void;
  emptyMessage?: string;
}

export function Combobox({
  options,
  value,
  setValue,
  placeholder = "Select an option",
  className,
  allowCustomValue = false,
  onCustomValueChange,
  emptyMessage,
}: ComboboxProps) {
  const { t } = useLanguage();
  const [open, setOpen] = React.useState(false);
  const [searchInput, setSearchInput] = React.useState("");
  
  // Handle keyboard input
  const handleKeyDown = (e: React.KeyboardEvent<HTMLDivElement>) => {
    if (e.key === "Enter") {
      e.preventDefault();
      
      if (allowCustomValue && searchInput && !options.includes(searchInput)) {
        setValue(searchInput);
        if (onCustomValueChange) onCustomValueChange(searchInput);
      }
      
      setOpen(false);
    }
  };
  
  // Get display value
  const displayValue = value || searchInput;
  
  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className={cn("w-full justify-between", className)}
        >
          <span className="truncate">{displayValue || placeholder}</span>
          <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-full p-0">
        <Command onKeyDown={handleKeyDown}>
          <CommandInput 
            placeholder={t('search') || 'Search...'} 
            value={searchInput}
            onValueChange={setSearchInput}
          />
          <CommandList>
            <CommandEmpty>{emptyMessage || t('noResults')}</CommandEmpty>
            <CommandGroup>
              {options.map((option) => (
                <CommandItem
                  key={option}
                  value={option}
                  onSelect={() => {
                    setValue(option);
                    setSearchInput("");
                    setOpen(false);
                  }}
                >
                  <Check
                    className={cn(
                      "mr-2 h-4 w-4",
                      value === option ? "opacity-100" : "opacity-0"
                    )}
                  />
                  {option}
                </CommandItem>
              ))}
              
              {allowCustomValue && searchInput && !options.includes(searchInput) && (
                <CommandItem
                  value={searchInput}
                  onSelect={() => {
                    setValue(searchInput);
                    if (onCustomValueChange) onCustomValueChange(searchInput);
                    setSearchInput("");
                    setOpen(false);
                  }}
                >
                  <span className="mr-2">{t('add') || 'Add'}:</span> {searchInput}
                </CommandItem>
              )}
            </CommandGroup>
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
}
