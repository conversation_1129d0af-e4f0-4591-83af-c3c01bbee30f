
import React from 'react';
import { useLanguage } from '@/hooks/useLanguage';
import { Input } from '@/components/ui/input';
import { MapPin } from 'lucide-react';

interface LocationFilterProps {
  location: string;
  setLocation: (value: string) => void;
}

const LocationFilter: React.FC<LocationFilterProps> = ({ location, setLocation }) => {
  const { t } = useLanguage();

  return (
    <div className="space-y-3">
      <h3 className="text-sm font-medium">{t('listings.location')}</h3>
      <div className="relative">
        <Input
          placeholder={t('listings.enterCity')}
          className="pl-10"
          value={location}
          onChange={(e) => setLocation(e.target.value)}
        />
        <MapPin className="absolute left-3 top-2.5 h-5 w-5 text-muted-foreground" />
      </div>
    </div>
  );
};

export default LocationFilter;
