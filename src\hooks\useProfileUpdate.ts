
import { useState } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useLanguage } from '@/hooks/useLanguage';
import { toast } from 'sonner';
import { createListingsImageBucket } from '@/utils/createStorageBuckets';

export type Profile = {
  id: string;
  username: string | null;
  full_name: string | null;
  avatar_url: string | null;
  phone: string | null;
};

export const useProfileUpdate = (
  profile: Profile, 
  onProfileUpdate: (updatedProfile: Profile) => void
) => {
  const { t } = useLanguage();
  const [username, setUsername] = useState(profile.username || '');
  const [fullName, setFullName] = useState(profile.full_name || '');
  const [phone, setPhone] = useState(profile.phone || '');
  const [avatarUrl, setAvatarUrl] = useState(profile.avatar_url || '');
  const [avatarFile, setAvatarFile] = useState<File | null>(null);
  const [isSaving, setIsSaving] = useState(false);

  const handleAvatarChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      setAvatarFile(file);
      
      const reader = new FileReader();
      reader.onload = (event) => {
        if (event.target?.result) {
          setAvatarUrl(event.target.result as string);
        }
      };
      reader.readAsDataURL(file);
    }
  };
  
  const handleSaveProfile = async () => {
    if (!profile.id) return;
    
    try {
      setIsSaving(true);
      
      let updatedAvatarUrl = avatarUrl;
      
      // Upload avatar if changed
      if (avatarFile) {
        const fileExt = avatarFile.name.split('.').pop();
        const fileName = `${profile.id}/avatar.${fileExt}`;
        
        // Make sure the bucket exists
        await createListingsImageBucket();
        
        const { error: uploadError, data } = await supabase.storage
          .from('listing_images')
          .upload(fileName, avatarFile, { upsert: true });
          
        if (uploadError) throw uploadError;
        
        const { data: urlData } = supabase.storage
          .from('listing_images')
          .getPublicUrl(fileName);
          
        updatedAvatarUrl = urlData.publicUrl;
      }

      // Update the profile using RPC - this gets around RLS issues
      const { error: updateError } = await supabase.rpc('update_profile', {
        p_id: profile.id,
        p_username: username,
        p_full_name: fullName,
        p_phone: phone,
        p_avatar_url: updatedAvatarUrl
      });

      if (updateError) {
        throw updateError;
      }
      
      toast.success(t('profileUpdated'), {
        description: t('profileUpdatedDesc')
      });
      
      const updatedProfile = {
        id: profile.id,
        username,
        full_name: fullName,
        phone,
        avatar_url: updatedAvatarUrl
      };
      
      onProfileUpdate(updatedProfile);
      
    } catch (error) {
      console.error('Error saving profile:', error);
      toast.error(t('errorSaving'), {
        description: t('errorSavingDesc')
      });
    } finally {
      setIsSaving(false);
    }
  };

  return {
    username,
    setUsername,
    fullName,
    setFullName,
    phone,
    setPhone,
    avatarUrl,
    avatarFile,
    isSaving,
    handleAvatarChange,
    handleSaveProfile
  };
};
