
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';
import { Listing } from '../types';

export const useDeleteListing = (
  setIsLoading: React.Dispatch<React.SetStateAction<boolean>>,
  setListings: React.Dispatch<React.SetStateAction<Listing[]>>,
  setFeaturedListings: React.Dispatch<React.SetStateAction<Listing[]>>
) => {
  const deleteListing = async (id: string) => {
    try {
      setIsLoading(true);
      
      const { error } = await supabase
        .from('listings')
        .delete()
        .eq('id', id);
        
      if (error) {
        throw error;
      }
      
      setListings(prevListings => 
        prevListings.filter(item => item.id !== id)
      );
      
      setFeaturedListings(prevFeatured => 
        prevFeatured.filter(item => item.id !== id)
      );
      
      toast.success("Listing deleted successfully", {
        description: "The listing has been removed"
      });
    } catch (error) {
      console.error('Error deleting listing:', error);
      toast.error("Error deleting listing", {
        description: "Failed to delete listing"
      });
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  return { deleteListing };
};
