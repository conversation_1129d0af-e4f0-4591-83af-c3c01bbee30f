
import React, { useState } from 'react';
import { useLanguage } from '@/hooks/useLanguage';
import InputFileUpload from './image-upload/InputFileUpload';
import ImagePreviewList from './image-upload/ImagePreviewList';

interface ImageUploaderProps {
  images: string[];
  setImages: (images: string[]) => void;
  maxImages: number;
}

const ImageUploader: React.FC<ImageUploaderProps> = ({
  images,
  setImages,
  maxImages
}) => {
  const { t } = useLanguage();
  const [previewUrls, setPreviewUrls] = useState<string[]>(images);
  
  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      const newFiles = Array.from(e.target.files);
      
      if (previewUrls.length + newFiles.length > maxImages) {
        alert(`You can upload a maximum of ${maxImages} images`);
        return;
      }
      
      // Create URLs for the files
      const newUrls = newFiles.map(file => URL.createObjectURL(file));
      
      // Update preview URLs
      const updatedUrls = [...previewUrls, ...newUrls];
      setPreviewUrls(updatedUrls);
      
      // Update parent component state
      setImages(updatedUrls);
    }
  };
  
  const handleRemoveImage = (index: number) => {
    const updatedUrls = [...previewUrls];
    updatedUrls.splice(index, 1);
    setPreviewUrls(updatedUrls);
    setImages(updatedUrls);
  };
  
  return (
    <div className="space-y-3">
      <InputFileUpload 
        onChange={handleImageChange} 
        helpText={t('imageHelp')}
      />
      
      <ImagePreviewList 
        previewUrls={previewUrls} 
        onRemoveImage={handleRemoveImage}
      />
    </div>
  );
};

export default ImageUploader;
