
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';
import { useAuth } from '@/contexts/AuthContext';

export const useTestListings = (fetchListings: () => Promise<void>) => {
  const { user } = useAuth();

  const createTestListings = async () => {
    if (user) {
      try {
        console.log("Creating test listings...");
        const testListings = [
          {
            title: "BMW X5 2018 - Engine and Transmission",
            description: "Complete engine and transmission from a 2018 BMW X5 in excellent condition. Low mileage and well maintained.",
            year: 2018,
            make: "BMW",
            model: "X5",
            price: 3500,
            location: "Los Angeles, CA",
            featured: true,
            status: "approved",
            image_urls: ["https://images.unsplash.com/photo-1619642751034-765dfdf7c58e?auto=format&fit=crop&q=80&w=1974&ixlib=rb-4.0.3"],
            user_id: user.id
          },
          {
            title: "Toyota Camry 2020 - Various Parts",
            description: "Various parts from a 2020 Toyota Camry including doors, lights, and interior components. All in good condition.",
            year: 2020,
            make: "Toyota",
            model: "Camry",
            price: 1200,
            location: "New York, NY",
            featured: false,
            status: "pending",
            image_urls: ["https://images.unsplash.com/photo-1617814076367-b759c7d7e738?auto=format&fit=crop&q=80&w=2574&ixlib=rb-4.0.3"],
            user_id: user.id
          },
          {
            title: "Mercedes-Benz E-Class 2019 - Front Bumper",
            description: "Original front bumper from a 2019 Mercedes-Benz E-Class. Minor scratches but overall good condition.",
            year: 2019,
            make: "Mercedes-Benz",
            model: "E-Class",
            price: 850,
            location: "Chicago, IL",
            featured: true,
            status: "approved",
            image_urls: ["https://images.unsplash.com/photo-1533473359331-0135ef1b58bf?auto=format&fit=crop&q=80&w=2670&ixlib=rb-4.0.3"],
            user_id: user.id
          },
          {
            title: "Ford F-150 2021 - Tailgate",
            description: "Original tailgate from a 2021 Ford F-150. Excellent condition, no dents or scratches.",
            year: 2021,
            make: "Ford",
            model: "F-150",
            price: 650,
            location: "Dallas, TX",
            featured: false,
            status: "pending",
            image_urls: ["https://images.unsplash.com/photo-1558618666-fcd25c85cd64?auto=format&fit=crop&q=80&w=2670&ixlib=rb-4.0.3"],
            user_id: user.id
          },
          {
            title: "Honda Civic 2019 - Headlights Set",
            description: "Complete set of headlights from a 2019 Honda Civic. LED technology, perfect working condition.",
            year: 2019,
            make: "Honda",
            model: "Civic",
            price: 450,
            location: "Miami, FL",
            featured: false,
            status: "approved",
            image_urls: ["https://images.unsplash.com/photo-1552519507-da3b142c6e3d?auto=format&fit=crop&q=80&w=2670&ixlib=rb-4.0.3"],
            user_id: user.id
          }
        ];

        for (const listing of testListings) {
          const { data, error } = await supabase.from('listings').insert(listing).select();
          if (error) {
            console.error('Error creating test listing:', error);
          } else {
            console.log('Test listing created:', data);
          }
        }

        toast.success("Test listings created", {
          description: "Test listings have been added to the database"
        });

        await fetchListings();
      } catch (error) {
        console.error('Error creating test listings:', error);
        toast.error("Error creating test listings", {
          description: "Failed to create test listings"
        });
      }
    } else {
      toast.error("Authentication required", {
        description: "You must be logged in to create test listings"
      });
    }
  };

  return { createTestListings };
};
