
import React, { useState, useEffect } from 'react';
import { useLanguage } from '@/hooks/useLanguage';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Separator } from '@/components/ui/separator';
import { Upload } from 'lucide-react';
import { toast } from 'sonner';
import { useSettings } from '@/hooks/useSettings';

const GeneralSettings: React.FC = () => {
  const { t } = useLanguage();
  const { settings, updateSettings } = useSettings();
  const [siteName, setSiteName] = useState(settings.general.site_name);
  const [siteDescription, setSiteDescription] = useState(settings.general.site_description);
  const [contactEmail, setContactEmail] = useState(settings.general.contact_email);
  const [isSaving, setIsSaving] = useState(false);
  const [logoFile, setLogoFile] = useState<File | null>(null);
  const [logoPreview, setLogoPreview] = useState(settings.general.logo_url || '/car-silhouette.svg');

  useEffect(() => {
    setSiteName(settings.general.site_name);
    setSiteDescription(settings.general.site_description);
    setContactEmail(settings.general.contact_email);
    setLogoPreview(settings.general.logo_url || '/car-silhouette.svg');
  }, [settings]);

  const handleLogoChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      setLogoFile(file);

      // Preview
      const reader = new FileReader();
      reader.onload = (event) => {
        if (event.target?.result) {
          setLogoPreview(event.target.result as string);
        }
      };
      reader.readAsDataURL(file);
    }
  };

  const handleSaveSettings = async () => {
    try {
      setIsSaving(true);

      // Prepare the general settings object
      const generalSettings: any = {
        site_name: siteName,
        site_description: siteDescription,
        contact_email: contactEmail
      };

      // If a new logo was uploaded, add it to settings
      if (logoFile) {
        generalSettings.logo_url = logoPreview;
      }

      console.log('Saving general settings:', generalSettings);

      // Save the settings
      await updateSettings('general', generalSettings);

      toast.success(t('admin.settingsSaved'));
    } catch (error) {
      console.error('Error saving settings:', error);
      toast.error(t('admin.errorSavingSettings'), {
        description: t('admin.tryAgain')
      });
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <div className="space-y-6">
      <div className="space-y-2">
        <Label htmlFor="siteName">{t('admin.siteName')}</Label>
        <Input
          id="siteName"
          value={siteName}
          onChange={(e) => setSiteName(e.target.value)}
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="siteDescription">{t('admin.siteDescription')}</Label>
        <Textarea
          id="siteDescription"
          value={siteDescription}
          onChange={(e) => setSiteDescription(e.target.value)}
          rows={3}
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="contactEmail">{t('admin.contactEmail')}</Label>
        <Input
          id="contactEmail"
          type="email"
          value={contactEmail}
          onChange={(e) => setContactEmail(e.target.value)}
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="logo">{t('admin.logo')}</Label>
        <div className="flex items-center gap-4">
          <div className="w-16 h-16 bg-secondary rounded-md flex items-center justify-center overflow-hidden">
            <img
              src={logoPreview}
              alt="Logo"
              className="w-full h-full object-contain"
            />
          </div>
          <div className="flex-1">
            <Input
              id="logo"
              type="file"
              accept="image/*"
              onChange={handleLogoChange}
              className="hidden"
            />
            <Button variant="outline" onClick={() => document.getElementById('logo')?.click()}>
              <Upload className="h-4 w-4 mr-2" />
              {t('admin.uploadLogo')}
            </Button>
          </div>
        </div>
      </div>

      <Separator className="my-4" />

      <Button onClick={handleSaveSettings} disabled={isSaving}>
        {isSaving ? t('admin.saving') : t('admin.saveChanges')}
      </Button>
    </div>
  );
};

export default GeneralSettings;
