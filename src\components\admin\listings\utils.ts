
import { Listing } from '@/hooks/listings/types';

export const paginateListings = (
  listings: Listing[],
  currentPage: number,
  itemsPerPage: number
) => {
  const totalPages = Math.max(1, Math.ceil(listings.length / itemsPerPage));
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentItems = listings.slice(indexOfFirstItem, indexOfLastItem);
  
  return {
    currentItems,
    totalPages,
  };
};
