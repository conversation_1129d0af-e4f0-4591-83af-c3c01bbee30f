
import React, { useState } from 'react';
import { useSettings } from '@/hooks/useSettings';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel } from '@/components/ui/form';
import { useForm } from 'react-hook-form';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Slider } from '@/components/ui/slider';
import { Button } from '@/components/ui/button';
import { toast } from 'sonner';
import { useLanguage } from '@/hooks/useLanguage';

const SecuritySettings = () => {
  const { t } = useLanguage();
  const { settings, isLoading, updateSettings } = useSettings();
  const [saving, setSaving] = useState(false);

  // Default values if settings aren't loaded yet
  const defaultValues = {
    two_factor_auth: false,
    password_policy: 'medium' as 'low' | 'medium' | 'high',
    session_timeout: 30
  };

  // Use settings from the server or default values
  const form = useForm({
    defaultValues: settings?.security || defaultValues
  });

  const onSubmit = async (data: typeof defaultValues) => {
    setSaving(true);
    try {
      await updateSettings('security', data);
      toast.success(t('settingsSaved'));
    } catch (error) {
      toast.error(t('errorSavingSettings'));
      console.error(error);
    } finally {
      setSaving(false);
    }
  };

  if (isLoading) {
    return (
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="h-4 w-3/4 animate-pulse bg-muted rounded"></CardTitle>
          <CardDescription className="h-3 w-1/2 animate-pulse bg-muted rounded mt-2"></CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="h-10 animate-pulse bg-muted rounded"></div>
          <div className="h-10 animate-pulse bg-muted rounded"></div>
          <div className="h-10 animate-pulse bg-muted rounded"></div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="mb-6">
      <CardHeader>
        <CardTitle>{t('admin.securitySettings')}</CardTitle>
        <CardDescription>{t('admin.securitySettingsDescription')}</CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <FormField
              control={form.control}
              name="two_factor_auth"
              render={({ field }) => (
                <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                  <div className="space-y-0.5">
                    <FormLabel className="text-base">
                      {t('admin.twoFactorAuth')}
                    </FormLabel>
                    <FormDescription>
                      {t('admin.twoFactorAuthDescription')}
                    </FormDescription>
                  </div>
                  <FormControl>
                    <Switch
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                </FormItem>
              )}
            />
            
            <FormField
              control={form.control}
              name="password_policy"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t('admin.passwordPolicy')}</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder={t('admin.selectPasswordPolicy')} />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="low">{t('admin.passwordPolicyLow')}</SelectItem>
                      <SelectItem value="medium">{t('admin.passwordPolicyMedium')}</SelectItem>
                      <SelectItem value="high">{t('admin.passwordPolicyHigh')}</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormDescription>
                    {t('admin.passwordPolicyDescription')}
                  </FormDescription>
                </FormItem>
              )}
            />
            
            <FormField
              control={form.control}
              name="session_timeout"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t('admin.sessionTimeout')}: {field.value} {t('admin.minutes')}</FormLabel>
                  <FormControl>
                    <Slider
                      min={5}
                      max={120}
                      step={5}
                      defaultValue={[field.value]}
                      onValueChange={(vals) => field.onChange(vals[0])}
                    />
                  </FormControl>
                  <FormDescription>
                    {t('admin.sessionTimeoutDescription')}
                  </FormDescription>
                </FormItem>
              )}
            />
            
            <Button type="submit" disabled={saving}>
              {saving ? t('admin.saving') : t('admin.saveChanges')}
            </Button>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
};

export default SecuritySettings;
