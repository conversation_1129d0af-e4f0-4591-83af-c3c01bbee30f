
import { useState, useEffect, useCallback } from "react";
import { supabase } from '@/integrations/supabase/client';
import { useToast } from "@/components/ui/use-toast";
import { useAuth } from "@/contexts/AuthContext";
import { toast } from "sonner";
import { Listing, UseListingsReturn } from "./types";
import { useListingsOperations } from "./useListingsOperations";
import { useTestListings } from "./useTestListings";

export * from './types';

export const useListings = (): UseListingsReturn => {
  const [listings, setListings] = useState<Listing[]>([]);
  const [featuredListings, setFeaturedListings] = useState<Listing[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  const { toast: uiToast } = useToast();
  const { user } = useAuth();

  // Fetch a single listing by ID
  const fetchListingById = async (id: string): Promise<Listing | null> => {
    try {
      setIsLoading(true);
      console.log(`Fetching listing with ID: ${id}`);

      const { data, error } = await supabase
        .from('listings')
        .select('*')
        .eq('id', id)
        .single();

      if (error) {
        console.error('Error fetching listing:', error);
        throw error;
      }

      console.log('Fetched listing:', data);
      return data as Listing;
    } catch (error) {
      console.error(`Error fetching listing with ID ${id}:`, error);
      setError(error as Error);
      uiToast({
        title: "Error loading",
        description: `Failed to load listing with ID ${id}`,
        variant: "destructive"
      });
      return null;
    } finally {
      setIsLoading(false);
    }
  };

  const fetchListings = useCallback(async () => {
    try {
      setIsLoading(true);
      console.log("Fetching all listings...");

      // Get unique listings by ID to prevent duplicates
      const { data, error } = await supabase
        .from('listings')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) {
        console.error("Error fetching listings:", error);
        throw error;
      }

      console.log("Fetched listings:", data);

      // Filter out duplicates by ID
      const uniqueListings = data ?
        Array.from(new Map(data.map(item => [item.id, item])).values()) :
        [];

      console.log("Unique listings:", uniqueListings);

      setListings(uniqueListings as Listing[]);

      const { data: featuredData, error: featuredError } = await supabase
        .from('listings')
        .select('*')
        .eq('featured', true)
        .order('created_at', { ascending: false })
        .limit(4);

      if (featuredError) {
        throw featuredError;
      }

      console.log("Fetched featured listings:", featuredData);

      // Filter out duplicates for featured listings too
      const uniqueFeaturedListings = featuredData ?
        Array.from(new Map(featuredData.map(item => [item.id, item])).values()) :
        [];

      setFeaturedListings(uniqueFeaturedListings as Listing[]);
    } catch (error) {
      console.error('Error fetching listings:', error);
      setError(error as Error);
      uiToast({
        title: "Error loading",
        description: "Failed to load listings",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  }, [uiToast]);

  const operations = useListingsOperations(setIsLoading, setListings, setFeaturedListings, fetchListings);
  const { createTestListings } = useTestListings(fetchListings);

  // Ensure the operation functions match the expected return types
  const featureListingWrapper = async (id: string, featured: boolean): Promise<void> => {
    await operations.featureListing(id, featured);
  };

  const setVipStatusWrapper = async (id: string, vipStatus: boolean): Promise<void> => {
    await operations.setVipStatus(id, vipStatus);
  };

  const setPaidStatusWrapper = async (id: string, paidStatus: boolean): Promise<void> => {
    await operations.setPaidStatus(id, paidStatus);
  };

  const updateListingStatusWrapper = async (id: string, status: 'pending' | 'approved' | 'rejected'): Promise<void> => {
    await operations.updateListingStatus(id, status);
  };

  const deleteListingWrapper = async (id: string): Promise<void> => {
    await operations.deleteListing(id);
  };

  // Fetch listings on component mount
  useEffect(() => {
    fetchListings();
  }, [fetchListings]);

  return {
    listings,
    featuredListings,
    isLoading,
    error,
    fetchListings,
    fetchListingById,
    createListing: operations.createListing,
    updateListing: operations.updateListing,
    deleteListing: deleteListingWrapper,
    featureListing: featureListingWrapper,
    setVipStatus: setVipStatusWrapper,
    setPaidStatus: setPaidStatusWrapper,
    updateListingStatus: updateListingStatusWrapper,
    createTestListings
  };
};
