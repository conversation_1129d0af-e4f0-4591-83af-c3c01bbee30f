
import i18next from 'i18next';
import { initReactI18next } from 'react-i18next';
import LanguageDetector from 'i18next-browser-languagedetector';
import { translations } from './translations';

// Export translations so they can be used elsewhere in the app
export { translations };

// Initialize i18next
i18next
  .use(LanguageDetector)
  .use(initReactI18next)
  .init({
    resources: translations,
    fallbackLng: 'en',
    interpolation: {
      escapeValue: false,
    },
    detection: {
      order: ['localStorage', 'navigator'],
      caches: ['localStorage'],
    },
    defaultNS: 'translation',
    debug: false,
    lng: localStorage.getItem('i18nextLng') || 'en', // Set initial language
    react: {
      useSuspense: false
    }
  });

// Ensure default language is set correctly on startup
const initializeLanguage = () => {
  const savedLang = localStorage.getItem('i18nextLng');
  const validLang = savedLang && ['en', 'ru', 'es'].includes(savedLang) ? savedLang : 'en';

  if (savedLang !== validLang) {
    localStorage.setItem('i18nextLng', validLang);
  }

  if (i18next.language !== validLang) {
    i18next.changeLanguage(validLang);
  }
};

// Initialize when the module loads
if (i18next.isInitialized) {
  initializeLanguage();
} else {
  i18next.on('initialized', initializeLanguage);
}

// Add a helper function to check if i18next is initialized
export const isI18nInitialized = () => {
  return i18next.isInitialized;
};

// Add a helper to force language refresh
export const refreshLanguage = () => {
  const currentLang = i18next.language;
  i18next.changeLanguage(currentLang);
  window.dispatchEvent(new Event('languageChanged'));
};

export default i18next;
