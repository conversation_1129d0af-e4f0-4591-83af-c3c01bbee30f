
import React from 'react';
import { Check } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useLanguage } from '@/hooks/useLanguage';
import { cn } from '@/lib/utils';

interface ListingItemProps {
  id: string;
  title: string;
  createdAt: string;
  status: string;
  onApprove: (id: string) => Promise<void>;
  isLoading: boolean;
}

const ListingItem: React.FC<ListingItemProps> = ({
  id,
  title,
  createdAt,
  status,
  onApprove,
  isLoading
}) => {
  const { t } = useLanguage();
  
  // Format time ago
  const formatTimeAgo = (dateString: string) => {
    const hours = Math.floor((Date.now() - new Date(dateString).getTime()) / (1000 * 60 * 60));
    if (hours === 1) {
      return `1 ${t('hourAgo')}`;
    }
    return `${hours} ${t('hoursAgo')}`;
  };
  
  return (
    <div className="flex items-center justify-between border-b py-2">
      <div>
        <p className="text-sm font-medium">{title}</p>
        <p className="text-xs text-muted-foreground">{formatTimeAgo(createdAt)}</p>
      </div>
      <div className="flex gap-2">
        <Button 
          variant="outline" 
          size="sm" 
          onClick={() => onApprove(id)}
          disabled={isLoading || status === 'approved'}
        >
          <Check className="h-3 w-3 mr-1" />
          {status === 'approved' ? t('admin.approved') : t('admin.approve')}
        </Button>
      </div>
    </div>
  );
};

export default ListingItem;
