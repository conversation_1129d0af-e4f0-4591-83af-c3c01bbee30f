
import React, { ReactNode } from 'react';
import Header from '@/components/Header';
import Footer from '@/components/Footer';

interface ListingDetailLayoutProps {
  children: ReactNode;
  isMobile: boolean;
}

const ListingDetailLayout: React.FC<ListingDetailLayoutProps> = ({ children, isMobile }) => {
  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      <main className="flex-grow container mx-auto px-4 py-4 relative">
        {children}
      </main>
      <Footer />
    </div>
  );
};

export default ListingDetailLayout;
