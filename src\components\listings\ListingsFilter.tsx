
import React, { useState } from 'react';
import { useLanguage } from '@/hooks/useLanguage';
import { Button } from '@/components/ui/button';
import {
  Sheet,
  SheetContent,
  SheetTrigger,
} from '@/components/ui/sheet';
import FilterHeader from './filter/FilterHeader';
import LocationFilter from './filter/LocationFilter';
import MakeModelFilter from './filter/MakeModelFilter';
import YearFilter from './filter/YearFilter';
import PriceFilter from './filter/PriceFilter';
import FilterActions from './filter/FilterActions';
import BodyTypeFilter from './filter/BodyTypeFilter';
import FuelTypeFilter from './filter/FuelTypeFilter';
import TransmissionFilter from './filter/TransmissionFilter';
import ColorFilter from './filter/ColorFilter';
import MileageFilter from './filter/MileageFilter';

interface ListingsFilterProps {
  onFilterApply: (filters: Record<string, any>) => void;
  onFilterReset: () => void;
}

const ListingsFilter: React.FC<ListingsFilterProps> = ({
  onFilterApply,
  onFilterReset
}) => {
  const { t } = useLanguage();
  const [make, setMake] = useState<string>('');
  const [model, setModel] = useState<string>('');
  const [yearFrom, setYearFrom] = useState<string>('');
  const [yearTo, setYearTo] = useState<string>('');
  const [priceMin, setPriceMin] = useState<string>('');
  const [priceMax, setPriceMax] = useState<string>('');
  const [location, setLocation] = useState<string>('');
  // New car filters
  const [bodyType, setBodyType] = useState<string>('');
  const [fuelType, setFuelType] = useState<string>('');
  const [transmission, setTransmission] = useState<string>('');
  const [color, setColor] = useState<string>('');
  const [mileageMin, setMileageMin] = useState<string>('');
  const [mileageMax, setMileageMax] = useState<string>('');

  // Handle the filter submission
  const handleApplyFilters = () => {
    const filters: Record<string, any> = {};

    if (make && make !== 'all') {
      filters.make = make;
    }

    if (model) {
      filters.model = model;
    }

    if (yearFrom) {
      filters.minYear = Number(yearFrom);
    }

    if (yearTo) {
      filters.maxYear = Number(yearTo);
    }

    if (priceMin) {
      filters.minPrice = Number(priceMin);
    }

    if (priceMax) {
      filters.maxPrice = Number(priceMax);
    }

    if (location) {
      filters.location = location;
    }

    // Add new car filters
    if (bodyType && bodyType !== 'all') {
      filters.body_type = bodyType;
    }

    if (fuelType && fuelType !== 'all') {
      filters.fuel_type = fuelType;
    }

    if (transmission && transmission !== 'all') {
      filters.transmission = transmission;
    }

    if (color && color !== 'all') {
      filters.color = color;
    }

    if (mileageMin) {
      filters.mileage_min = Number(mileageMin);
    }

    if (mileageMax) {
      filters.mileage_max = Number(mileageMax);
    }

    onFilterApply(filters);
  };

  const handleReset = () => {
    setMake('');
    setModel('');
    setYearFrom('');
    setYearTo('');
    setPriceMin('');
    setPriceMax('');
    setLocation('');
    // Reset new car filters
    setBodyType('');
    setFuelType('');
    setTransmission('');
    setColor('');
    setMileageMin('');
    setMileageMax('');
    onFilterReset();
  };

  return (
    <Sheet>
      <SheetTrigger>
        <div className="w-full bg-background rounded-md border border-input p-3 hover:bg-accent hover:text-accent-foreground transition-colors">
          {t('ui.filter')}
        </div>
      </SheetTrigger>
      <SheetContent className="w-[300px] sm:w-[400px] overflow-y-auto max-h-screen">
        <FilterHeader />
        <div className="py-6 space-y-6">
          <LocationFilter location={location} setLocation={setLocation} />
          <MakeModelFilter
            make={make}
            setMake={setMake}
            model={model}
            setModel={setModel}
          />
          <YearFilter
            yearFrom={yearFrom}
            setYearFrom={setYearFrom}
            yearTo={yearTo}
            setYearTo={setYearTo}
          />
          <PriceFilter
            priceMin={priceMin}
            setPriceMin={setPriceMin}
            priceMax={priceMax}
            setPriceMax={setPriceMax}
          />
          {/* New car filters */}
          <BodyTypeFilter
            bodyType={bodyType}
            setBodyType={setBodyType}
          />
          <FuelTypeFilter
            fuelType={fuelType}
            setFuelType={setFuelType}
          />
          <TransmissionFilter
            transmission={transmission}
            setTransmission={setTransmission}
          />
          <ColorFilter
            color={color}
            setColor={setColor}
          />
          <MileageFilter
            mileageMin={mileageMin}
            setMileageMin={setMileageMin}
            mileageMax={mileageMax}
            setMileageMax={setMileageMax}
          />
          <FilterActions onReset={handleReset} onApply={handleApplyFilters} />
        </div>
      </SheetContent>
    </Sheet>
  );
};

export default ListingsFilter;
