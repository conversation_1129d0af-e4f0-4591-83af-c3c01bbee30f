-- Add status column to listings table
-- Status can be: pending, approved, rejected, blocked, active

-- Create enum for listing status
CREATE TYPE listing_status AS ENUM ('pending', 'approved', 'rejected', 'blocked', 'active');

-- Add status column to listings table
ALTER TABLE listings 
ADD COLUMN status listing_status DEFAULT 'pending';

-- Add index for better performance when filtering by status
CREATE INDEX idx_listings_status ON listings(status);

-- Update existing listings to have 'active' status if they are featured
UPDATE listings 
SET status = 'active' 
WHERE featured = true;

-- Update remaining listings to 'approved' status
UPDATE listings 
SET status = 'approved' 
WHERE status = 'pending' AND featured = false;

-- Add comment to explain the status field
COMMENT ON COLUMN listings.status IS 'Status of the listing: pending (awaiting moderation), approved (approved but not active), rejected (rejected by moderator), blocked (blocked by admin), active (live and visible)';
