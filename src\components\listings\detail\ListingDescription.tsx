
import React from 'react';
import { useLanguage } from '@/hooks/useLanguage';

interface ListingDescriptionProps {
  description: string;
}

const ListingDescription: React.FC<ListingDescriptionProps> = ({ description }) => {
  const { t } = useLanguage();
  
  // Get translated text immediately to ensure proper rendering
  const descriptionTitle = t('listings.description');
  const noDescriptionText = t('listings.noDescriptionProvided');

  return (
    <div className="space-y-4">
      <h2 className="text-xl font-bold">{descriptionTitle}</h2>
      <div className="text-muted-foreground whitespace-pre-line prose max-w-none dark:prose-invert prose-p:my-2">
        {description
          ? description
            .split('\n')
            .map((paragraph, index) =>
              paragraph.trim()
                ? <p key={index}>{paragraph}</p>
                : <br key={index} />
            )
          : <p>{noDescriptionText}</p>
        }
      </div>
    </div>
  );
};

export default ListingDescription;
