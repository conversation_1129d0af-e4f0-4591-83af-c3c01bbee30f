
import { Database } from '@/integrations/supabase/types';

// Extend the base type from Supabase with our additional fields
export type ListingRow = Database['public']['Tables']['listings']['Row'];

// NewListing type based on actual database schema
export interface NewListing extends Omit<ListingRow, 'id' | 'created_at' | 'updated_at'> {
  status: string; // Make status required for new listings
}

// Extended Listing type that includes our client-side properties
export interface Listing extends ListingRow {
  vip_status?: boolean;
  paid_status?: boolean;
  status: string; // Make status required
}

export interface UseListingsReturn {
  listings: Listing[];
  featuredListings: Listing[];
  isLoading: boolean;
  error: Error | null;
  fetchListings: () => Promise<void>;
  fetchListingById: (id: string) => Promise<Listing | null>;
  createListing: (listing: Omit<NewListing, 'user_id'>) => Promise<Listing>;
  updateListing: (id: string, listing: Partial<Listing>) => Promise<Listing>;
  deleteListing: (id: string) => Promise<void>;
  featureListing: (id: string, featured: boolean) => Promise<void>;
  setVipStatus: (id: string, vipStatus: boolean) => Promise<void>;
  setPaidStatus: (id: string, paidStatus: boolean) => Promise<void>;
  createTestListings: () => Promise<void>;
  updateListingStatus?: (id: string, status: string) => Promise<void>;
}
