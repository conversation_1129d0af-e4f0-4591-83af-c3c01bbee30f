
import React from 'react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useLanguage } from '@/hooks/useLanguage';

export type SortOption = 'newest' | 'oldest' | 'price_asc' | 'price_desc';

interface SortSelectorProps {
  value: SortOption;
  onChange: (value: SortOption) => void;
  className?: string;
}

const SortSelector: React.FC<SortSelectorProps> = ({ value, onChange, className }) => {
  const { t } = useLanguage();

  return (
    <div className={`flex items-center gap-2 ${className}`}>
      <span className="text-sm whitespace-nowrap">{t('listings.sortBy')}:</span>
      <Select
        value={value}
        onValueChange={(val) => onChange(val as SortOption)}
      >
        <SelectTrigger className="w-auto min-w-[130px]">
          <SelectValue placeholder={t('listings.newest')} />
        </SelectTrigger>
        <SelectContent align="end">
          <SelectItem value="newest">{t('listings.newest')}</SelectItem>
          <SelectItem value="oldest">{t('listings.oldest')}</SelectItem>
          <SelectItem value="price_asc">{t('listings.priceLowToHigh')}</SelectItem>
          <SelectItem value="price_desc">{t('listings.priceHighToLow')}</SelectItem>
        </SelectContent>
      </Select>
    </div>
  );
};

export default SortSelector;
