import React from 'react';
import { useLanguage } from '@/hooks/useLanguage';
import { Button } from '@/components/ui/button';
import { Check } from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';

interface PendingListingItemProps {
  id: string;
  title: string;
  created_at: string;
  onApprove: () => Promise<void>;
}

const PendingListingItem: React.FC<PendingListingItemProps> = ({
  id,
  title,
  created_at,
  onApprove
}) => {
  const { t } = useLanguage();
  
  // Format time ago
  const formatTimeAgo = (dateString: string) => {
    try {
      return formatDistanceToNow(new Date(dateString), { addSuffix: true });
    } catch (e) {
      return dateString;
    }
  };
  
  return (
    <div className="flex items-center justify-between border-b py-2">
      <div>
        <p className="text-sm font-medium">{title}</p>
        <p className="text-xs text-muted-foreground">
          {formatTimeAgo(created_at)}
        </p>
      </div>
      <div className="flex gap-2">
        <Button 
          variant="outline" 
          size="sm" 
          onClick={onApprove}
          className="flex items-center gap-1"
        >
          <Check className="h-3 w-3" />
          {t('admin.approve')}
        </Button>
      </div>
    </div>
  );
};

export default PendingListingItem;
