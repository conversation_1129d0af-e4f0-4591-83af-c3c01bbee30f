
import React from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Input } from '@/components/ui/input';
import { useLanguage } from '@/hooks/useLanguage';

interface ProfileAvatarProps {
  avatarUrl: string;
  fullName: string;
  username: string;
  userEmail?: string;
  onAvatarChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
}

const ProfileAvatar: React.FC<ProfileAvatarProps> = ({
  avatarUrl,
  fullName,
  username,
  userEmail,
  onAvatarChange
}) => {
  const { t } = useLanguage();

  return (
    <Card>
      <CardHeader>
        <CardTitle>{t('profile.avatar')}</CardTitle>
      </CardHeader>
      <CardContent className="flex flex-col items-center">
        <Avatar className="w-32 h-32 mb-4">
          {avatarUrl ? (
            <AvatarImage src={avatarUrl} alt={fullName || username || ""} />
          ) : null}
          <AvatarFallback className="text-3xl">
            {(fullName || username || userEmail || "")
              .charAt(0)
              .toUpperCase()}
          </AvatarFallback>
        </Avatar>

        <div className="w-full">
          <label htmlFor="avatar-upload" className="block text-sm font-medium text-muted-foreground mb-2">
            {t('profile.selectFile')}
          </label>
          <Input
            id="avatar-upload"
            type="file"
            accept="image/*"
            onChange={onAvatarChange}
            className="mt-2"
          />
        </div>
      </CardContent>
    </Card>
  );
};

export default ProfileAvatar;
