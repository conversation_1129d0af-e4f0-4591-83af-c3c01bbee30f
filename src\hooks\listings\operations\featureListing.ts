
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';
import { Listing } from '../types';

export const useFeatureListing = (
  setIsLoading: React.Dispatch<React.SetStateAction<boolean>>,
  setListings: React.Dispatch<React.SetStateAction<Listing[]>>,
  setFeaturedListings: React.Dispatch<React.SetStateAction<Listing[]>>
) => {
  const featureListing = async (id: string, isFeatured: boolean): Promise<void> => {
    try {
      setIsLoading(true);
      
      const { data, error } = await supabase
        .from('listings')
        .update({ featured: isFeatured })
        .eq('id', id)
        .select()
        .single();
        
      if (error) {
        throw error;
      }
      
      // Update listings in state - ensure we're preserving the extended properties
      setListings(prevListings => 
        prevListings.map(item => item.id === id ? {...item, featured: isFeatured} : item)
      );
      
      // Update featured listings
      if (isFeatured) {
        setFeaturedListings(prev => {
          const exists = prev.some(item => item.id === id);
          if (!exists) {
            // Create a variable to hold the listing to add
            let listingToAdd: Listing | undefined;
            
            // Find the listing in the listings state
            setListings(prevListings => {
              listingToAdd = prevListings.find(item => item.id === id);
              return prevListings;
            });
            
            // If we found the listing, add it to the featured listings
            if (listingToAdd) {
              return [{...listingToAdd, featured: true}, ...prev].slice(0, 4); // Keep only top 4 featured
            }
            return prev;
          }
          return prev.map(item => item.id === id ? {...item, featured: true} : item);
        });
      } else {
        setFeaturedListings(prev => 
          prev.filter(item => item.id !== id)
        );
      }
    } catch (error) {
      console.error('Error updating featured status:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  return { featureListing };
};
