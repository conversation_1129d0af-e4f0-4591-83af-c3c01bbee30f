
import { supabase } from '@/integrations/supabase/client';

export const createListingsImageBucket = async () => {
  try {
    // Check if bucket exists
    const { data: buckets, error: bucketsError } = await supabase
      .storage
      .listBuckets();
    
    if (bucketsError) {
      console.error('Error listing buckets:', bucketsError);
      return false;
    }
    
    const bucketExists = buckets?.some(bucket => bucket.name === 'listing_images');
    
    if (!bucketExists) {
      // Create the bucket if it doesn't exist
      const { error } = await supabase
        .storage
        .createBucket('listing_images', {
          public: true,
          fileSizeLimit: 10485760, // 10MB
        });
      
      if (error) {
        console.error('Error creating bucket:', error);
        return false;
      }
      
      console.log('Bucket created successfully');
      return true;
    }
    
    return true;
  } catch (error) {
    console.error('Error checking/creating bucket:', error);
    return false;
  }
};
