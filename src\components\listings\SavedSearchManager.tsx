import React, { useState, useEffect } from 'react';
import { useLanguage } from '@/hooks/useLanguage';
import { useAuth } from '@/contexts/AuthContext';
import { supabase } from '@/integrations/supabase/client';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/components/ui/alert-dialog';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { toast } from 'sonner';
import { <PERSON>, Bell, <PERSON>Off, Trash2, Play, BookmarkPlus } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { useIsMobile } from '@/hooks/use-mobile';

interface SavedSearch {
  id: string;
  name: string;
  search_term: string;
  filters: any;
  notify: boolean;
  created_at: string;
}

interface SavedSearchManagerProps {
  onApplySearch?: (searchTerm: string, filters: any) => void;
  className?: string;
}

const SavedSearchManager: React.FC<SavedSearchManagerProps> = ({
  onApplySearch,
  className = ''
}) => {
  const { t } = useLanguage();
  const { user, isAuthenticated } = useAuth();
  const navigate = useNavigate();
  const isMobile = useIsMobile();
  const [savedSearches, setSavedSearches] = useState<SavedSearch[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isOpen, setIsOpen] = useState(false);

  useEffect(() => {
    if (isAuthenticated && user) {
      fetchSavedSearches();
    }
  }, [isAuthenticated, user]);

  const fetchSavedSearches = async () => {
    if (!user) return;

    try {
      setIsLoading(true);
      const { data, error } = await supabase
        .from('saved_searches')
        .select('*')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false });

      if (error) throw error;
      setSavedSearches(data || []);
    } catch (error) {
      console.error('Error fetching saved searches:', error);
      toast.error(t('listings.errorLoadingSavedSearches'));
    } finally {
      setIsLoading(false);
    }
  };

  const handleToggleNotifications = async (id: string, currentValue: boolean) => {
    try {
      const { error } = await supabase
        .from('saved_searches')
        .update({ notify: !currentValue })
        .eq('id', id);

      if (error) throw error;

      setSavedSearches(searches =>
        searches.map(search =>
          search.id === id ? { ...search, notify: !currentValue } : search
        )
      );

      toast.success(currentValue ? t('listings.notificationsDisabled') : t('listings.notificationsEnabled'));
    } catch (error) {
      console.error('Error updating notification status:', error);
      toast.error(t('listings.errorUpdatingNotifications'));
    }
  };

  const handleDeleteSearch = async (id: string) => {
    try {
      const { error } = await supabase
        .from('saved_searches')
        .delete()
        .eq('id', id);

      if (error) throw error;

      setSavedSearches(searches => searches.filter(search => search.id !== id));
      toast.success(t('listings.savedSearchDeleted'));
    } catch (error) {
      console.error('Error deleting saved search:', error);
      toast.error(t('listings.errorDeletingSavedSearch'));
    }
  };

  const handleApplySearch = (search: SavedSearch) => {
    if (onApplySearch) {
      onApplySearch(search.search_term, search.filters);
      setIsOpen(false);
      toast.success(t('listings.savedSearchApplied'));
    } else {
      // Navigate to listings page with search parameters
      const params = new URLSearchParams();
      if (search.search_term) {
        params.set('search', search.search_term);
      }
      if (search.filters && Object.keys(search.filters).length > 0) {
        Object.entries(search.filters).forEach(([key, value]) => {
          if (value) {
            params.set(key, String(value));
          }
        });
      }
      navigate(`/listings?${params.toString()}`);
      setIsOpen(false);
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString();
  };

  if (!isAuthenticated) {
    return (
      <Button variant="outline" onClick={() => navigate('/auth')}>
        <BookmarkPlus className="h-4 w-4 mr-2" />
        {t('listings.signInToSaveSearches')}
      </Button>
    );
  }

  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        <Button variant="outline" className={className}>
          <BookmarkPlus className="h-4 w-4 mr-2" />
          {t('listings.savedSearches')}
          {savedSearches.length > 0 && (
            <Badge variant="secondary" className="ml-2">
              {savedSearches.length}
            </Badge>
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent className={`${isMobile ? 'w-80' : 'w-96'} p-0`} align="end">
        <Card className="border-0 shadow-none">
          <CardHeader className="pb-3">
            <CardTitle className="text-lg flex items-center gap-2">
              <BookmarkPlus className="h-5 w-5" />
              {t('listings.savedSearches')}
            </CardTitle>
          </CardHeader>
          <CardContent className="pt-0">
            {isLoading ? (
              <div className="text-center py-4">
                <p className="text-sm text-muted-foreground">{t('ui.loading')}...</p>
              </div>
            ) : savedSearches.length === 0 ? (
              <div className="text-center py-8">
                <Search className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <p className="text-sm text-muted-foreground mb-2">
                  {t('listings.noSavedSearches')}
                </p>
                <p className="text-xs text-muted-foreground">
                  {t('listings.saveSearchesToGetNotified')}
                </p>
              </div>
            ) : (
              <div className="space-y-3 max-h-96 overflow-y-auto">
                {savedSearches.map((search) => (
                  <div
                    key={search.id}
                    className="border rounded-lg p-3 space-y-2"
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1 min-w-0">
                        <h4 className="font-medium text-sm truncate">
                          {search.name}
                        </h4>
                        {search.search_term && (
                          <p className="text-xs text-muted-foreground truncate">
                            "{search.search_term}"
                          </p>
                        )}
                        <p className="text-xs text-muted-foreground">
                          {formatDate(search.created_at)}
                        </p>
                      </div>

                      <div className="flex items-center gap-1 ml-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleApplySearch(search)}
                          className="h-7 w-7 p-0"
                          title={t('listings.applySearch')}
                        >
                          <Play className="h-3 w-3" />
                        </Button>

                        <AlertDialog>
                          <AlertDialogTrigger asChild>
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-7 w-7 p-0 text-destructive hover:text-destructive"
                              title={t('listings.deleteSearch')}
                            >
                              <Trash2 className="h-3 w-3" />
                            </Button>
                          </AlertDialogTrigger>
                          <AlertDialogContent>
                            <AlertDialogHeader>
                              <AlertDialogTitle>{t('listings.confirmDelete')}</AlertDialogTitle>
                              <AlertDialogDescription>
                                {t('listings.deleteSavedSearchWarning')}
                              </AlertDialogDescription>
                            </AlertDialogHeader>
                            <AlertDialogFooter>
                              <AlertDialogCancel>{t('ui.cancel')}</AlertDialogCancel>
                              <AlertDialogAction
                                onClick={() => handleDeleteSearch(search.id)}
                                className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                              >
                                {t('ui.delete')}
                              </AlertDialogAction>
                            </AlertDialogFooter>
                          </AlertDialogContent>
                        </AlertDialog>
                      </div>
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <Switch
                          id={`notify-${search.id}`}
                          checked={search.notify}
                          onCheckedChange={() => handleToggleNotifications(search.id, search.notify)}
                        />
                        <Label htmlFor={`notify-${search.id}`} className="text-xs">
                          {search.notify ? (
                            <span className="flex items-center gap-1 text-green-600">
                              <Bell className="h-3 w-3" />
                              {t('listings.notificationsOn')}
                            </span>
                          ) : (
                            <span className="flex items-center gap-1 text-muted-foreground">
                              <BellOff className="h-3 w-3" />
                              {t('listings.notificationsOff')}
                            </span>
                          )}
                        </Label>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </PopoverContent>
    </Popover>
  );
};

export default SavedSearchManager;
