import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import { useLanguage } from '@/hooks/useLanguage';
import { useAuth } from '@/contexts/AuthContext';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';
import { Listing } from '@/hooks/listings/types';
import ProfileInfo from '@/components/profile/ProfileInfo';
import UserListings from '@/components/profile/UserListings';
import FavoriteListings from '@/components/profile/FavoriteListings';
import ProfileLoader from '@/components/profile/ProfileLoader';
import BackButton from '@/components/BackButton';

type Profile = {
  id: string;
  username: string | null;
  full_name: string | null;
  avatar_url: string | null;
  phone: string | null;
};

const Profile: React.FC = () => {
  const { t } = useLanguage();
  const navigate = useNavigate();
  const { user, isAuthenticated, isLoading: authLoading } = useAuth();

  const [profile, setProfile] = useState<Profile | null>(null);
  const [userListings, setUserListings] = useState<Listing[]>([]);
  const [favoriteListings, setFavoriteListings] = useState<Listing[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    if (!authLoading && !isAuthenticated) {
      toast.error(t('ui.authRequired'), {
        description: t('profile.loginToAccessProfile')
      });
      navigate('/auth');
    }
  }, [isAuthenticated, authLoading, navigate, t]);

  useEffect(() => {
    const fetchProfile = async () => {
      if (!user) return;

      try {
        setIsLoading(true);

        const { data: profileData, error: profileError } = await supabase
          .from('profiles')
          .select('*')
          .eq('id', user.id)
          .single();

        if (profileError && profileError.code !== 'PGRST116') {
          throw profileError;
        }

        setProfile(profileData || { id: user.id, username: null, full_name: null, avatar_url: null, phone: null });

        const { data: listingsData, error: listingsError } = await supabase
          .from('listings')
          .select('*')
          .eq('user_id', user.id)
          .order('created_at', { ascending: false });

        if (listingsError) {
          throw listingsError;
        }

        setUserListings(listingsData || []);

        const { data: favoritesData, error: favoritesError } = await supabase
          .from('favorites')
          .select(`
            listing_id,
            listings:listing_id (*)
          `)
          .eq('user_id', user.id);

        if (favoritesError) {
          throw favoritesError;
        }

        const favoriteListingsData = favoritesData?.map(fav => fav.listings) || [];
        setFavoriteListings(favoriteListingsData);

      } catch (error) {
        console.error('Error fetching profile data:', error);
        toast.error(t('ui.errorLoading'), {
          description: t('profile.errorLoadingProfile')
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchProfile();
  }, [user, t]);

  const handleProfileUpdate = (updatedProfile: Profile) => {
    setProfile(updatedProfile);
  };

  if (isLoading || !profile) {
    return <ProfileLoader />;
  }

  return (
    <div className="min-h-screen flex flex-col">
      <Header />

      <main className="flex-grow pt-20">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-10">
          <div className="max-w-4xl mx-auto">
            <BackButton to="/" />

            <h1 className="text-2xl md:text-3xl font-bold mb-6">
              {t('profile.profile')}
            </h1>

            <Tabs defaultValue="profile" className="space-y-6">
              <TabsList>
                <TabsTrigger value="profile">{t('profile.profile')}</TabsTrigger>
                <TabsTrigger value="listings">{t('profile.myListings')}</TabsTrigger>
                <TabsTrigger value="favorites">{t('profile.favorites')}</TabsTrigger>
              </TabsList>

              <TabsContent value="profile">
                <ProfileInfo
                  profile={profile}
                  userEmail={user?.email}
                  onProfileUpdate={handleProfileUpdate}
                />
              </TabsContent>

              <TabsContent value="listings">
                <UserListings listings={userListings} />
              </TabsContent>

              <TabsContent value="favorites">
                <FavoriteListings listings={favoriteListings} />
              </TabsContent>
            </Tabs>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
};

export default Profile;
