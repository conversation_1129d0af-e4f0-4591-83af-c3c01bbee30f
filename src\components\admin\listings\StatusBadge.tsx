import React from 'react';
import { Badge } from '@/components/ui/badge';
import { useLanguage } from '@/hooks/useLanguage';
import { ListingStatus } from '@/types/admin';
import { cn } from '@/lib/utils';

interface StatusBadgeProps {
  status: ListingStatus;
  className?: string;
}

const StatusBadge: React.FC<StatusBadgeProps> = ({ status, className }) => {
  const { t } = useLanguage();

  const getStatusConfig = (status: ListingStatus) => {
    switch (status) {
      case 'pending':
        return {
          label: t('admin.pending'),
          variant: 'secondary' as const,
          className: 'bg-yellow-100 text-yellow-800 border-yellow-200 hover:bg-yellow-200'
        };
      case 'approved':
        return {
          label: t('admin.approved'),
          variant: 'secondary' as const,
          className: 'bg-green-100 text-green-800 border-green-200 hover:bg-green-200'
        };
      case 'rejected':
        return {
          label: t('admin.rejected'),
          variant: 'destructive' as const,
          className: 'bg-red-100 text-red-800 border-red-200 hover:bg-red-200'
        };
      case 'blocked':
        return {
          label: t('admin.blocked'),
          variant: 'secondary' as const,
          className: 'bg-gray-100 text-gray-800 border-gray-200 hover:bg-gray-200'
        };
      case 'active':
        return {
          label: t('admin.active'),
          variant: 'default' as const,
          className: 'bg-blue-100 text-blue-800 border-blue-200 hover:bg-blue-200'
        };
      default:
        return {
          label: status,
          variant: 'secondary' as const,
          className: 'bg-gray-100 text-gray-800 border-gray-200'
        };
    }
  };

  const config = getStatusConfig(status);

  return (
    <Badge
      variant={config.variant}
      className={cn(config.className, className)}
    >
      {config.label}
    </Badge>
  );
};

export default StatusBadge;
