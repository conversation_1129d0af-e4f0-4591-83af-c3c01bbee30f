
import React from 'react';
import { useLanguage } from '@/hooks/useLanguage';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { MoreHorizontal, UserCog, UserMinus, LockKeyhole, Star, CreditCard } from 'lucide-react';
import { ProtectedAction } from '@/components/auth/ProtectedAction';

interface UserActionMenuProps {
  userId: string;
  status: string;
  vipStatus?: boolean;
  paidStatus?: boolean;
  onRoleChange: (userId: string, newRole: string) => void;
  onStatusChange: (userId: string, newStatus: string) => void;
  onPasswordReset: (userId: string) => void;
  onVipStatusChange?: (userId: string, newStatus: boolean) => void;
  onPaidStatusChange?: (userId: string, newStatus: boolean) => void;
}

const UserActionMenu: React.FC<UserActionMenuProps> = ({
  userId,
  status,
  vipStatus = false,
  paidStatus = false,
  onRoleChange,
  onStatusChange,
  onPasswordReset,
  onVipStatusChange,
  onPaidStatusChange,
}) => {
  const { t } = useLanguage();

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" size="sm">
          <MoreHorizontal className="h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuLabel>{t('admin.actions')}</DropdownMenuLabel>
        <DropdownMenuSeparator />
        <DropdownMenuItem onClick={() => onRoleChange(userId, 'admin')}>
          <UserCog className="h-4 w-4 mr-2" />
          {t('admin.changeRole')}
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => onPasswordReset(userId)}>
          <LockKeyhole className="h-4 w-4 mr-2" />
          {t('admin.resetPassword')}
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => onStatusChange(userId, status === 'active' ? 'inactive' : 'active')}>
          <UserMinus className="h-4 w-4 mr-2" />
          {status === 'active' ? t('admin.suspend') : t('admin.unsuspend')}
        </DropdownMenuItem>

        {onVipStatusChange && (
          <DropdownMenuItem onClick={() => onVipStatusChange(userId, !vipStatus)}>
            <Star className="h-4 w-4 mr-2" />
            {vipStatus ? t('admin.removeVip') : t('admin.setVip')}
          </DropdownMenuItem>
        )}

        {onPaidStatusChange && (
          <DropdownMenuItem onClick={() => onPaidStatusChange(userId, !paidStatus)}>
            <CreditCard className="h-4 w-4 mr-2" />
            {paidStatus ? t('admin.markAsUnpaid') : t('admin.markAsPaid')}
          </DropdownMenuItem>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default UserActionMenu;
