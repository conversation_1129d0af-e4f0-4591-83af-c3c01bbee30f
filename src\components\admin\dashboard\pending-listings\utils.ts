import { Listing } from '@/hooks/listings/types';

export const paginateListings = (
  listings: any[],
  currentPage: number,
  itemsPerPage: number
) => {
  const totalPages = Math.max(1, Math.ceil(listings.length / itemsPerPage));
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentItems = listings.slice(indexOfFirstItem, indexOfLastItem);
  
  return {
    currentItems,
    totalPages,
  };
};

export const filterListingsByStatus = (
  listings: Listing[],
  status: 'featured' | 'vip' | 'paid' | null,
  value: boolean | null = null
): Listing[] => {
  if (!status) return listings;
  
  return listings.filter(listing => {
    if (status === 'featured') {
      return value === null ? listing.featured : listing.featured === value;
    }
    if (status === 'vip') {
      return value === null ? listing.vip_status : listing.vip_status === value;
    }
    if (status === 'paid') {
      return value === null ? listing.paid_status : listing.paid_status === value;
    }
    return true;
  });
};

export const filterUsersByStatus = (
  users: any[],
  statusType: 'role' | 'status' | 'vip' | 'paid',
  value: string | boolean | null = null
): any[] => {
  if (!statusType || value === null) return users;
  
  return users.filter(user => {
    if (statusType === 'role') {
      return value === 'all' || user.role === value;
    }
    if (statusType === 'status') {
      return value === 'all' || user.status === value;
    }
    if (statusType === 'vip') {
      return user.vipStatus === value;
    }
    if (statusType === 'paid') {
      return user.paidStatus === value;
    }
    return true;
  });
};
