
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { useAuth } from '@/contexts/AuthContext';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';
import { useLanguage } from '@/hooks/useLanguage';
import { Bell, Loader2 } from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Input } from '@/components/ui/input';

interface SaveSearchButtonProps {
  searchTerm: string;
  filters: Record<string, any>;
}

const SaveSearchButton: React.FC<SaveSearchButtonProps> = ({ searchTerm, filters }) => {
  const { t } = useLanguage();
  const { user, isAuthenticated } = useAuth();
  const [isSaving, setIsSaving] = useState(false);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [notifyEnabled, setNotifyEnabled] = useState(true);
  const [searchName, setSearchName] = useState('');

  const handleSaveSearch = async () => {
    if (!isAuthenticated || !user) {
      toast.error(t('listings.authRequired'), {
        description: t('listings.pleaseLoginToSaveSearch')
      });
      return;
    }

    try {
      setIsSaving(true);

      // Create search criteria object
      const searchCriteria = {
        user_id: user.id,
        name: searchName.trim() || searchTerm || t('listings.savedSearch'),
        search_term: searchTerm,
        filters: filters,
        notify: notifyEnabled,
        created_at: new Date().toISOString()
      };

      // Use type assertion to bypass TypeScript checks
      const { error } = await (supabase
        .from('saved_searches') as any)
        .insert([searchCriteria]);

      if (error) throw error;

      toast.success(t('listings.searchSaved'), {
        description: notifyEnabled ? t('listings.notificationsEnabled') : t('listings.noNotifications')
      });

      setIsDialogOpen(false);
    } catch (error) {
      console.error('Error saving search:', error);
      toast.error(t('listings.errorSavingSearch'));
    } finally {
      setIsSaving(false);
    }
  };

  const openSaveDialog = () => {
    if (!isAuthenticated) {
      toast.error(t('listings.authRequired'), {
        description: t('listings.pleaseLoginToSaveSearch')
      });
      return;
    }

    setIsDialogOpen(true);
  };

  return (
    <>
      <Button
        variant="outline"
        size="sm"
        className="flex items-center gap-2"
        onClick={openSaveDialog}
      >
        <Bell className="h-4 w-4" />
        {t('listings.saveSearch')}
      </Button>

      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>{t('listings.saveSearch')}</DialogTitle>
            <DialogDescription>
              {t('listings.saveSearchDescription')}
            </DialogDescription>
          </DialogHeader>

          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="searchName" className="text-right">
                {t('listings.name')}
              </Label>
              <Input
                id="searchName"
                placeholder={searchTerm || t('listings.savedSearch')}
                value={searchName}
                onChange={(e) => setSearchName(e.target.value)}
                className="col-span-3"
              />
            </div>

            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="notifications" className="text-right">
                {t('listings.notifications')}
              </Label>
              <div className="flex items-center space-x-2 col-span-3">
                <Switch
                  id="notifications"
                  checked={notifyEnabled}
                  onCheckedChange={setNotifyEnabled}
                />
                <Label htmlFor="notifications" className="text-sm text-muted-foreground">
                  {t('listings.notifyNewListings')}
                </Label>
              </div>
            </div>
          </div>

          <DialogFooter>
            <Button
              type="submit"
              onClick={handleSaveSearch}
              disabled={isSaving}
            >
              {isSaving && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              {t('listings.saveSearch')}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default SaveSearchButton;
