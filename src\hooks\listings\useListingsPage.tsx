
import { useState, useEffect, useCallback } from 'react';
import { useSearchParams } from 'react-router-dom';
import { useListings } from '@/hooks/listings/useListings';
import { useTestListings } from '@/hooks/listings/useTestListings';
import { toast } from 'sonner';
import { useLanguage } from '@/hooks/useLanguage';
import { useListingsFilter } from './filters/useListingsFilter';
import { usePagination } from './filters/usePagination';
import { useUrlParams } from './filters/useUrlParams';

export const useListingsPage = () => {
  const { t } = useLanguage();
  const [searchParams] = useSearchParams();
  const { updateUrlParams } = useUrlParams();
  const locationFromUrl = searchParams.get('location');

  // Basic state
  const [searchTerm, setSearchTerm] = useState(locationFromUrl || '');
  const { listings, isLoading, fetchListings } = useListings();
  const { createTestListings } = useTestListings(fetchListings);
  
  // Pre-translate all texts to ensure proper rendering
  const refreshingText = t('listings.refreshingListings');
  const refreshedText = t('listings.listingsRefreshed');
  const refreshErrorText = t('listings.refreshError');
  const filtersAppliedText = t('listings.filtersApplied');
  const testListingsCreatedText = t('listings.testListingsCreated');
  const testListingsErrorText = t('listings.testListingsError');

  // Initialize filtering
  const {
    filteredListings,
    sortBy,
    setSortBy,
    activeFilters,
    handleFilterApply
  } = useListingsFilter(listings, searchTerm);

  // Initialize pagination
  const {
    currentPage,
    totalPages,
    handlePageChange,
    getPaginatedItems
  } = usePagination(filteredListings.length);

  // Get paginated listings
  const paginatedListings = getPaginatedItems(filteredListings);

  // Update URL when searchTerm or page changes
  const handleUpdateUrlParams = useCallback((page: number) => {
    updateUrlParams({
      'page': page.toString(),
      'location': searchTerm || null
    });
  }, [updateUrlParams, searchTerm]);

  // Force a refresh on component mount
  useEffect(() => {
    fetchListings().catch(error => {
      console.error("Error loading listings on mount:", error);
      toast.error(t('listings.listingsFetchError'));
    });
  }, [fetchListings, t]);

  // Update searchTerm when URL changes
  useEffect(() => {
    if (locationFromUrl) {
      setSearchTerm(locationFromUrl);
    }
  }, [locationFromUrl]);

  // Handlers
  const handleRefresh = async () => {
    try {
      toast.loading(refreshingText);
      await fetchListings();
      toast.success(refreshedText);
    } catch (error) {
      console.error("Error refreshing listings:", error);
      toast.error(refreshErrorText);
    }
  };

  const handleFilterApplyWithToast = useCallback((filters: Record<string, any>) => {
    handleFilterApply(filters);
    handleUpdateUrlParams(1);
    toast.success(filtersAppliedText);
  }, [handleFilterApply, handleUpdateUrlParams, filtersAppliedText]);

  const handleCreateTestListings = async () => {
    try {
      toast.loading(refreshingText);
      await createTestListings();
      toast.success(testListingsCreatedText);
    } catch (error) {
      console.error("Error creating test listings:", error);
      toast.error(testListingsErrorText);
    }
  };

  const handlePageChangeWithUrl = useCallback((page: number) => {
    handlePageChange(page);
    handleUpdateUrlParams(page);
  }, [handlePageChange, handleUpdateUrlParams]);

  return {
    searchTerm,
    setSearchTerm,
    sortBy,
    setSortBy,
    listings,
    filteredListings,
    paginatedListings,
    isLoading,
    currentPage,
    totalPages,
    handlePageChange: handlePageChangeWithUrl,
    handleRefresh,
    handleFilterApply: handleFilterApplyWithToast,
    handleCreateTestListings,
    activeFilters,
    updateUrlParams: handleUpdateUrlParams,
    // Export pre-translated strings for components to use
    translations: {
      refreshingText,
      refreshedText,
      refreshErrorText
    }
  };
};
