
import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import Logo from '@/components/header/Logo';

interface AdminPageLayoutProps {
  title: string;
  children: React.ReactNode;
}

const AdminPageLayout: React.FC<AdminPageLayoutProps> = ({ title, children }) => {
  return (
    <div className="container py-6">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-4">
          <Logo />
        </div>
        <h1 className="text-2xl font-bold">{title}</h1>
      </div>
      {children}
    </div>
  );
};

export default AdminPageLayout;
