
import React from 'react';
import { Input } from '@/components/ui/input';
import { Search } from 'lucide-react';
import { useLanguage } from '@/hooks/useLanguage';

interface SearchBarProps {
  searchTerm?: string;
  onSearchChange: (value: string) => void;
  placeholder?: string;
  className?: string;
}

const SearchBar: React.FC<SearchBarProps> = ({ 
  searchTerm = '', 
  onSearchChange, 
  placeholder, 
  className = "" 
}) => {
  const { t } = useLanguage();
  const defaultPlaceholder = t('listings.search');
  
  return (
    <div className={`relative ${className}`}>
      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
      <Input
        type="text"
        value={searchTerm}
        onChange={(e) => onSearchChange(e.target.value)}
        placeholder={placeholder || defaultPlaceholder}
        className="pl-10 h-12"
      />
    </div>
  );
};

export default SearchBar;
