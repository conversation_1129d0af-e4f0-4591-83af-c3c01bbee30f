
import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useLanguage } from '@/hooks/useLanguage';
import { useAuth } from '@/contexts/AuthContext';
import { useListings } from '@/hooks/listings/useListings';
import { toast } from 'sonner';
import { ListingFormData } from './types';

export const useListingFormSubmit = (
  isEdit: boolean = false,
  existingData?: any
) => {
  const { t } = useLanguage();
  const navigate = useNavigate();
  const { isAuthenticated } = useAuth();
  const { createListing, updateListing } = useListings();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async (
    formData: ListingFormData,
    customMake: string,
    customModel: string,
    customLocation: string
  ) => {
    if (!isAuthenticated) {
      toast.error(t('auth.authenticationRequired'));
      navigate('/auth');
      return;
    }

    try {
      setIsSubmitting(true);

      // Проверка обязательных полей
      if (!formData.title.trim()) {
        toast.error(t('listings.titleRequired') || 'Заголовок обязателен');
        return;
      }

      if (!formData.make && !customMake) {
        toast.error(t('listings.makeRequired') || 'Марка обязательна');
        return;
      }

      if (!formData.model && !customModel) {
        toast.error(t('listings.modelRequired') || 'Модель обязательна');
        return;
      }

      if (!formData.location && !customLocation) {
        toast.error(t('listings.locationRequired') || 'Местоположение обязательно');
        return;
      }

      // Проверка числовых полей
      const price = Number(formData.price);
      if (isNaN(price) || price <= 0) {
        toast.error(t('listings.invalidPrice') || 'Неверная цена');
        return;
      }

      const year = Number(formData.year);
      if (isNaN(year) || year < 1900 || year > new Date().getFullYear()) {
        toast.error(t('listings.invalidYear') || 'Неверный год');
        return;
      }

      const listingData = {
        title: formData.title.trim(),
        description: formData.description?.trim() || '',
        price: price,
        year: year,
        make: formData.make || customMake,
        model: formData.model || customModel,
        location: formData.location || customLocation,
        featured: formData.featured,
        image_urls: formData.images,
        status: 'active' // Add default status
        // Note: bodyType, fuelType, transmission, color, mileage are not stored in database
        // These fields are for UI only until database schema is updated
      };

      console.log('Submitting listing data:', listingData);

      if (isEdit && existingData?.id) {
        await updateListing(existingData.id, listingData);
        toast.success(t('listings.listingUpdated') || 'Объявление обновлено');
      } else {
        await createListing(listingData);
        toast.success(t('listings.listingCreated') || 'Объявление создано');
      }

      navigate('/listings');
    } catch (error: any) {
      console.error('Error submitting listing:', error);
      // Error messages are handled by the backend operations (createListing/updateListing)
      // No need to display additional error messages here to avoid duplicates
    } finally {
      setIsSubmitting(false);
    }
  };

  return {
    isSubmitting,
    handleSubmit
  };
};
