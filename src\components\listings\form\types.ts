
import { z } from 'zod';
import { UseFormReturn } from 'react-hook-form';

export interface ListingFormProps {
  onCancel: () => void;
  existingData?: any;
  isEdit?: boolean;
}

export const listingFormSchema = z.object({
  title: z.string().min(1, { message: "Заголовок обязателен" }),
  description: z.string().optional(),
  price: z.string().or(z.number()).refine(value => {
    const num = Number(value);
    return !isNaN(num) && num > 0;
  }, { message: "Цена должна быть больше 0" }),
  year: z.number().min(1900, { message: "Год должен быть не менее 1900" }).max(new Date().getFullYear(), { message: "Год не может быть больше текущего" }),
  make: z.string().min(1, { message: "Марка обязательна" }),
  model: z.string().min(1, { message: "Модель обязательна" }),
  location: z.string().min(1, { message: "Местоположение обязательно" }),
  featured: z.boolean().default(false),
  images: z.array(z.string()).default([]),
  bodyType: z.string().optional(),
  fuelType: z.string().optional(),
  transmission: z.string().optional(),
  color: z.string().optional(),
  mileage: z.string().optional(),
});

export type ListingFormData = z.infer<typeof listingFormSchema>;

export interface FormContextType {
  form: UseFormReturn<ListingFormData>;
  customMake: string;
  setCustomMake: (value: string) => void;
  customModel: string;
  setCustomModel: (value: string) => void;
  customLocation: string;
  setCustomLocation: (value: string) => void;
}

export interface FormSectionProps {
  form: UseFormReturn<ListingFormData>;
}
