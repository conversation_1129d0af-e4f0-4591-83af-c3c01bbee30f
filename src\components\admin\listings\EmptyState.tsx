
import React from 'react';
import { useLanguage } from '@/hooks/useLanguage';
import { Button } from '@/components/ui/button';

interface EmptyStateProps {
  onClearFilters: () => void;
}

const EmptyState: React.FC<EmptyStateProps> = ({ onClearFilters }) => {
  const { t } = useLanguage();
  
  return (
    <div className="flex flex-col items-center justify-center h-64 text-center">
      <p className="text-muted-foreground mb-4">{t('admin.noResultsFound')}</p>
      <Button onClick={onClearFilters}>
        {t('admin.clearFilters')}
      </Button>
    </div>
  );
};

export default EmptyState;
