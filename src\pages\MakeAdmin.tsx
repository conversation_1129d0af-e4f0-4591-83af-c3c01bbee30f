import React, { useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { supabase } from '@/integrations/supabase/client';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { toast } from 'sonner';
import { Shield, UserCheck, AlertTriangle } from 'lucide-react';
import Header from '@/components/Header';
import Footer from '@/components/Footer';

/**
 * Временная утилита для назначения административных прав
 * Эта страница должна быть удалена в продакшене!
 */
const MakeAdmin: React.FC = () => {
  const { user, isAuthenticated } = useAuth();
  const [email, setEmail] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [currentUserAdmin, setCurrentUserAdmin] = useState(false);

  const makeCurrentUserAdmin = async () => {
    if (!user) {
      toast.error('Пользователь не авторизован');
      return;
    }

    setIsLoading(true);
    try {
      // Обновляем профиль текущего пользователя
      const { error } = await supabase
        .from('profiles')
        .upsert({
          id: user.id,
          username: user.email?.split('@')[0] || 'admin',
          full_name: user.user_metadata?.full_name || 'Admin User',
          is_admin: true,
          role: 'admin'
        });

      if (error) {
        throw error;
      }

      setCurrentUserAdmin(true);
      toast.success('Вы успешно назначены администратором!', {
        description: 'Обновите страницу, чтобы увидеть административную панель'
      });
    } catch (error) {
      console.error('Error making user admin:', error);
      toast.error('Ошибка при назначении прав администратора', {
        description: error instanceof Error ? error.message : 'Неизвестная ошибка'
      });
    } finally {
      setIsLoading(false);
    }
  };

  const makeUserAdminByEmail = async () => {
    if (!email.trim()) {
      toast.error('Введите email пользователя');
      return;
    }

    setIsLoading(true);
    try {
      // Ищем пользователя по email в auth.users через profiles
      const { data: profiles, error: searchError } = await supabase
        .from('profiles')
        .select('id, username, full_name')
        .ilike('username', email.split('@')[0]);

      if (searchError) {
        throw searchError;
      }

      if (!profiles || profiles.length === 0) {
        toast.error('Пользователь не найден', {
          description: 'Убедитесь, что пользователь зарегистрирован в системе'
        });
        return;
      }

      // Обновляем права для найденного пользователя
      const { error: updateError } = await supabase
        .from('profiles')
        .update({
          is_admin: true,
          role: 'admin'
        })
        .eq('id', profiles[0].id);

      if (updateError) {
        throw updateError;
      }

      toast.success(`Пользователь ${email} назначен администратором!`);
      setEmail('');
    } catch (error) {
      console.error('Error making user admin:', error);
      toast.error('Ошибка при назначении прав администратора', {
        description: error instanceof Error ? error.message : 'Неизвестная ошибка'
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      
      <main className="flex-grow container mx-auto py-8 px-4">
        <div className="max-w-2xl mx-auto space-y-6">
          {/* Предупреждение */}
          <Card className="border-orange-200 bg-orange-50">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-orange-800">
                <AlertTriangle className="h-5 w-5" />
                Внимание: Утилита для разработки
              </CardTitle>
              <CardDescription className="text-orange-700">
                Эта страница предназначена только для настройки прав администратора в процессе разработки. 
                В продакшене она должна быть удалена!
              </CardDescription>
            </CardHeader>
          </Card>

          {/* Назначение себя администратором */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <UserCheck className="h-5 w-5" />
                Назначить себя администратором
              </CardTitle>
              <CardDescription>
                Назначить административные права текущему пользователю
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {isAuthenticated ? (
                <div className="space-y-4">
                  <div className="p-4 bg-muted rounded-lg">
                    <p className="text-sm">
                      <strong>Текущий пользователь:</strong> {user?.email}
                    </p>
                    <p className="text-sm text-muted-foreground">
                      ID: {user?.id}
                    </p>
                  </div>
                  
                  {currentUserAdmin ? (
                    <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                      <p className="text-green-800 font-medium">
                        ✅ Вы уже назначены администратором!
                      </p>
                      <p className="text-green-700 text-sm mt-1">
                        Обновите страницу, чтобы увидеть административную панель в навигации.
                      </p>
                    </div>
                  ) : (
                    <Button 
                      onClick={makeCurrentUserAdmin}
                      disabled={isLoading}
                      className="w-full"
                    >
                      {isLoading ? 'Назначение...' : 'Назначить меня администратором'}
                    </Button>
                  )}
                </div>
              ) : (
                <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                  <p className="text-yellow-800">
                    Войдите в систему, чтобы назначить себя администратором
                  </p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Назначение другого пользователя администратором */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Shield className="h-5 w-5" />
                Назначить администратора по email
              </CardTitle>
              <CardDescription>
                Назначить административные права пользователю по email адресу
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="email">Email пользователя</Label>
                <Input
                  id="email"
                  type="email"
                  placeholder="<EMAIL>"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                />
              </div>
              
              <Button 
                onClick={makeUserAdminByEmail}
                disabled={isLoading || !email.trim()}
                className="w-full"
                variant="outline"
              >
                {isLoading ? 'Назначение...' : 'Назначить администратором'}
              </Button>
            </CardContent>
          </Card>

          {/* Инструкции */}
          <Card>
            <CardHeader>
              <CardTitle>Инструкции</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3 text-sm">
              <div>
                <strong>1. Для текущего пользователя:</strong>
                <p className="text-muted-foreground">
                  Нажмите "Назначить меня администратором", затем обновите страницу (F5)
                </p>
              </div>
              
              <div>
                <strong>2. Для другого пользователя:</strong>
                <p className="text-muted-foreground">
                  Введите email пользователя и нажмите "Назначить администратором"
                </p>
              </div>
              
              <div>
                <strong>3. После назначения:</strong>
                <p className="text-muted-foreground">
                  В навигации появится ссылка "Панель администратора"
                </p>
              </div>
              
              <div className="p-3 bg-red-50 border border-red-200 rounded">
                <strong className="text-red-800">Важно:</strong>
                <p className="text-red-700 text-xs mt-1">
                  Удалите эту страницу перед развертыванием в продакшене!
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      </main>
      
      <Footer />
    </div>
  );
};

export default MakeAdmin;
