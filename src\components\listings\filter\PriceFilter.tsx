
import React from 'react';
import { useLanguage } from '@/hooks/useLanguage';
import { Input } from '@/components/ui/input';

interface PriceFilterProps {
  priceMin: string;
  setPriceMin: (value: string) => void;
  priceMax: string;
  setPriceMax: (value: string) => void;
}

const PriceFilter: React.FC<PriceFilterProps> = ({
  priceMin,
  setPriceMin,
  priceMax,
  setPriceMax
}) => {
  const { t } = useLanguage();

  return (
    <div className="space-y-3">
      <h3 className="text-sm font-medium">{t('listings.price')}</h3>
      <div className="flex gap-3">
        <Input
          placeholder={t('listings.priceFrom')}
          type="number"
          value={priceMin}
          onChange={(e) => setPriceMin(e.target.value)}
        />
        <Input
          placeholder={t('listings.priceTo')}
          type="number"
          value={priceMax}
          onChange={(e) => setPriceMax(e.target.value)}
        />
      </div>
    </div>
  );
};

export default PriceFilter;
