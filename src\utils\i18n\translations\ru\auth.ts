
export const auth = {
  "signIn": "Войти",
  "signUp": "Зарегистрироваться",
  "logout": "Выйти",
  "email": "Электронная почта",
  "password": "Пароль",
  "confirmPassword": "Подтвердите пароль",
  "login": "Войти",
  "register": "Зарегистрироваться",
  "signInWithGoogle": "Войти через Google",
  "firstName": "Имя",
  "lastName": "Фамилия",
  "enterCredentials": "Введите данные для входа в ваш аккаунт",
  "createAccount": "Создайте аккаунт, чтобы начать",
  "dontHaveAccount": "Нет аккаунта?",
  "alreadyHaveAccount": "Уже есть аккаунт?",
  "authenticationRequired": "Требуется авторизация",
  "mustBeLoggedInToCreateListing": "Вы должны войти в систему, чтобы создать объявление",
  "pleaseEnterEmailAndPassword": "Пожалуйста, введите email и пароль",
  "pleaseEnterAllFields": "Пожалуйста, заполните все обязательные поля",
  "accountCreated": "Аккаунт создан",
  "youCanNowSignIn": "Теперь вы можете войти, используя свои данные",
  "authError": "Ошибка авторизации",
  "unknownError": "Произошла неизвестная ошибка",
  "orContinueWith": "или продолжить с",
  "signingIn": "Вход в систему...",
  "signingUp": "Регистрация...",
  "allFieldsRequired": "Все поля обязательны",
  "welcomeBack": "Добро пожаловать обратно"
};
