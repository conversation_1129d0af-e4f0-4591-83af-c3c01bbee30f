
import { toast } from 'sonner';

// Define types for our settings
export interface SiteSettings {
  general: {
    site_name: string;
    site_description: string;
    contact_email: string;
    logo_url?: string;
  };
  appearance: {
    primary_color: string;
    dark_mode: boolean;
  };
  notifications: {
    email_notifications: boolean;
    push_notifications: boolean;
    new_listing_notifications: boolean;
    message_notifications: boolean;
  };
  security: {
    two_factor_auth: boolean;
    password_policy: 'low' | 'medium' | 'high';
    session_timeout: number;
  };
  listings: {
    auto_approve: boolean;
  };
}

// Default settings
const defaultSettings: SiteSettings = {
  general: {
    site_name: 'Auto Parts Market',
    site_description: 'Find the perfect auto parts for your vehicle.',
    contact_email: '<EMAIL>',
  },
  appearance: {
    primary_color: '#0066ff',
    dark_mode: true,
  },
  notifications: {
    email_notifications: true,
    push_notifications: false,
    new_listing_notifications: true,
    message_notifications: true,
  },
  security: {
    two_factor_auth: false,
    password_policy: 'medium',
    session_timeout: 30,
  },
  listings: {
    auto_approve: false,
  }
};

// Load settings from localStorage
export const loadSettings = (): SiteSettings => {
  try {
    const savedSettings = localStorage.getItem('site_settings');
    if (savedSettings) {
      // Load saved settings and ensure new fields are initialized
      const parsed = JSON.parse(savedSettings);
      
      // Initialize any new settings categories that might not exist
      if (!parsed.listings) {
        parsed.listings = defaultSettings.listings;
      }
      
      return {
        ...defaultSettings,
        ...parsed
      };
    }
  } catch (error) {
    console.error('Error loading settings:', error);
  }
  // If no settings found or error, return default
  return defaultSettings;
};

// Save settings to localStorage
export const saveSettings = (
  category: keyof SiteSettings, 
  data: Partial<SiteSettings[keyof SiteSettings]>
): Promise<boolean> => {
  return new Promise((resolve, reject) => {
    try {
      console.log(`Saving ${category} settings:`, data);
      
      // Get current settings
      const currentSettings = loadSettings();
      
      // Create updated settings object
      const updatedSettings = {
        ...currentSettings,
        [category]: {
          ...currentSettings[category],
          ...data
        }
      };
      
      console.log('Updated settings:', updatedSettings);
      
      // Save to localStorage
      localStorage.setItem('site_settings', JSON.stringify(updatedSettings));
      
      // Apply theme settings immediately if it's appearance
      if (category === 'appearance') {
        if ('primary_color' in data) {
          document.documentElement.style.setProperty('--primary', data.primary_color as string);
        }
        if ('dark_mode' in data) {
          document.documentElement.classList.toggle('dark', data.dark_mode as boolean);
        }
      }
      
      resolve(true);
    } catch (error) {
      console.error(`Error saving ${category} settings:`, error);
      reject(error);
    }
  });
};
