
import { useEffect } from 'react';

export const useLanguageChangeEffect = (callback: () => void) => {
  useEffect(() => {
    console.log('Language change effect hook initialized');
    
    const handleLanguageChange = () => {
      console.log('Language changed event detected');
      callback();
    };
    
    document.addEventListener('languageChanged', handleLanguageChange);
    
    return () => {
      document.removeEventListener('languageChanged', handleLanguageChange);
    };
  }, [callback]);
};
