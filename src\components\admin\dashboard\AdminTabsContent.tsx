
import React from 'react';
import { TabsContent } from '@/components/ui/tabs';
import AdminOverview from './AdminOverview';
import EnhancedAdminListings from '@/components/admin/EnhancedAdminListings';
import AdminUsers from '@/components/admin/AdminUsers';
import AnalyticsDashboard from '@/components/admin/analytics/AnalyticsDashboard';
import AdminSettings from '@/components/admin/AdminSettings';

const AdminTabsContent: React.FC = () => {
  return (
    <>
      <TabsContent value="overview" className="space-y-6">
        <AdminOverview />
      </TabsContent>

      <TabsContent value="listings">
        <EnhancedAdminListings />
      </TabsContent>

      <TabsContent value="users">
        <AdminUsers />
      </TabsContent>

      <TabsContent value="analytics">
        <AnalyticsDashboard />
      </TabsContent>

      <TabsContent value="settings">
        <AdminSettings />
      </TabsContent>
    </>
  );
};

export default AdminTabsContent;
