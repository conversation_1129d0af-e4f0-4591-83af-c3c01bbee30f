import React from 'react';
import { useAdminAccess } from '@/hooks/useAdminAccess';
import { useAuth } from '@/contexts/AuthContext';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import Header from '@/components/Header';
import Footer from '@/components/Footer';

/**
 * Простая тестовая страница для проверки прав администратора
 */
const TestAdmin: React.FC = () => {
  const { user, isAuthenticated } = useAuth();
  const { isAdmin, isLoading, error } = useAdminAccess();

  console.log('TestAdmin - Full state:', {
    user: user?.id,
    email: user?.email,
    isAuthenticated,
    isAdmin,
    isLoading,
    error
  });

  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      
      <main className="flex-grow container mx-auto py-8 px-4">
        <div className="max-w-2xl mx-auto space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Тест прав администратора</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <strong>Аутентификация:</strong>
                  <span className={isAuthenticated ? 'text-green-600' : 'text-red-600'}>
                    {isAuthenticated ? ' ✅ Да' : ' ❌ Нет'}
                  </span>
                </div>
                
                <div>
                  <strong>Email:</strong>
                  <span className="ml-2">{user?.email || 'Не указан'}</span>
                </div>
                
                <div>
                  <strong>Загрузка:</strong>
                  <span className={isLoading ? 'text-yellow-600' : 'text-gray-600'}>
                    {isLoading ? ' ⏳ Да' : ' ✅ Нет'}
                  </span>
                </div>
                
                <div>
                  <strong>Администратор:</strong>
                  <span className={isAdmin ? 'text-green-600' : 'text-red-600'}>
                    {isAdmin ? ' ✅ Да' : ' ❌ Нет'}
                  </span>
                </div>
                
                <div className="col-span-2">
                  <strong>Ошибка:</strong>
                  <span className={error ? 'text-red-600' : 'text-green-600'}>
                    {error ? ` ❌ ${error}` : ' ✅ Нет ошибок'}
                  </span>
                </div>
              </div>
              
              {isAuthenticated && (
                <div className="p-4 bg-muted rounded">
                  <strong>ID пользователя:</strong> {user?.id}
                </div>
              )}
              
              <div className="space-y-2">
                <h4 className="font-semibold">Результат проверки:</h4>
                {!isAuthenticated && (
                  <div className="p-3 bg-red-50 border border-red-200 rounded text-red-800">
                    ❌ Пользователь не авторизован
                  </div>
                )}
                
                {isAuthenticated && isLoading && (
                  <div className="p-3 bg-yellow-50 border border-yellow-200 rounded text-yellow-800">
                    ⏳ Проверка прав администратора...
                  </div>
                )}
                
                {isAuthenticated && !isLoading && error && (
                  <div className="p-3 bg-red-50 border border-red-200 rounded text-red-800">
                    ❌ Ошибка: {error}
                  </div>
                )}
                
                {isAuthenticated && !isLoading && !error && !isAdmin && (
                  <div className="p-3 bg-orange-50 border border-orange-200 rounded text-orange-800">
                    ⚠️ Пользователь авторизован, но не является администратором
                  </div>
                )}
                
                {isAuthenticated && !isLoading && !error && isAdmin && (
                  <div className="p-3 bg-green-50 border border-green-200 rounded text-green-800">
                    ✅ Пользователь является администратором!
                  </div>
                )}
              </div>
              
              {isAdmin && (
                <div className="space-y-2">
                  <Button asChild className="w-full">
                    <a href="/admin">Перейти в админ панель</a>
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader>
              <CardTitle>Инструкции по отладке</CardTitle>
            </CardHeader>
            <CardContent className="text-sm space-y-2">
              <p>1. Откройте консоль браузера (F12 → Console)</p>
              <p>2. Посмотрите на сообщения, начинающиеся с "TestAdmin"</p>
              <p>3. Если isAdmin = false, проверьте данные профиля в базе</p>
              <p>4. Если есть ошибки, они будут показаны выше</p>
            </CardContent>
          </Card>
        </div>
      </main>
      
      <Footer />
    </div>
  );
};

export default TestAdmin;
