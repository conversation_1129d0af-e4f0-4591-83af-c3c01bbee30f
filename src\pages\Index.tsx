import React, { useEffect, useState } from 'react';
import Header from '@/components/Header';
import Hero from '@/components/Hero';
import FeaturedListings from '@/components/FeaturedListings';
import Footer from '@/components/Footer';
import { useLanguage } from '@/hooks/useLanguage';
import { Button } from '@/components/ui/button';
import { Search, Car, BarChart, Shield } from 'lucide-react';
import { useListings } from '@/hooks/useListings';
import { Link } from 'react-router-dom';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';

const Index: React.FC = () => {
  const { t, language } = useLanguage();
  const { listings, fetchListings } = useListings();
  const [isCreatingTestData, setIsCreatingTestData] = useState(false);

  // Create test listings if needed
  useEffect(() => {
    if (listings.length === 0 && !isCreatingTestData) {
      createTestListings();
    }
  }, [listings.length]);

  // Function to create test listings
  const createTestListings = async (force = false) => {
    setIsCreatingTestData(true);
    try {
      // Check if we already have listings
      const { count, error: countError } = await supabase
        .from('listings')
        .select('*', { count: 'exact', head: true });

      if (countError) throw countError;

      // Only create test data if no listings exist or if forced
      if (count === 0 || force) {
        // Generate valid UUIDs for test users
        const generateUUID = () => {
          return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
            const r = Math.random() * 16 | 0;
            const v = c == 'x' ? r : (r & 0x3 | 0x8);
            return v.toString(16);
          });
        };

        const testListings = [
          {
            title: 'BMW X5 Engine 3.0L Diesel 2015-2018',
            make: 'BMW',
            model: 'X5',
            year: 2018,
            price: 3500,
            location: 'Munich, Germany',
            description: 'Complete engine assembly with all accessories. Low mileage, excellent condition. Compatible with BMW X5 models from 2015 to 2018.',
            image_urls: ['https://images.unsplash.com/photo-1619642751034-765dfdf7c58e?auto=format&fit=crop&q=80&w=1974&ixlib=rb-4.0.3'],
            featured: true,
            status: 'approved',
            user_id: generateUUID()
          },
          {
            title: 'Mercedes-Benz C-Class Headlight Assembly 2019',
            make: 'Mercedes-Benz',
            model: 'C-Class',
            year: 2019,
            price: 450,
            location: 'Stuttgart, Germany',
            description: 'Original driver side headlight assembly for Mercedes-Benz C-Class 2019. LED lighting, perfect working condition, no damage.',
            image_urls: ['https://images.unsplash.com/photo-1565034946487-077786996e27?auto=format&fit=crop&q=80&w=1974&ixlib=rb-4.0.3'],
            featured: true,
            status: 'approved',
            user_id: generateUUID()
          },
          {
            title: 'Toyota Camry Transmission Automatic 2020',
            make: 'Toyota',
            model: 'Camry',
            year: 2020,
            price: 1200,
            location: 'Tokyo, Japan',
            description: 'Automatic transmission from Toyota Camry 2020. Only 15,000 miles, removed from a car with front-end damage. Works perfectly.',
            image_urls: ['https://images.unsplash.com/photo-1578760039515-20dbc5aa9ed3?auto=format&fit=crop&q=80&w=1974&ixlib=rb-4.0.3'],
            featured: false,
            status: 'pending',
            user_id: generateUUID()
          },
          {
            title: 'Ford F-150 Tailgate 2021',
            make: 'Ford',
            model: 'F-150',
            year: 2021,
            price: 650,
            location: 'Dallas, TX',
            description: 'Original tailgate from Ford F-150 2021. Excellent condition, no dents or scratches.',
            image_urls: ['https://images.unsplash.com/photo-1558618666-fcd25c85cd64?auto=format&fit=crop&q=80&w=2670&ixlib=rb-4.0.3'],
            featured: false,
            status: 'pending',
            user_id: generateUUID()
          },
          {
            title: 'Honda Civic Headlights Set 2019',
            make: 'Honda',
            model: 'Civic',
            year: 2019,
            price: 450,
            location: 'Miami, FL',
            description: 'Complete LED headlights set from Honda Civic 2019. Perfect working condition.',
            image_urls: ['https://images.unsplash.com/photo-1552519507-da3b142c6e3d?auto=format&fit=crop&q=80&w=2670&ixlib=rb-4.0.3'],
            featured: false,
            status: 'approved',
            user_id: generateUUID()
          }
        ];

        // Insert test listings
        const { error: insertError } = await supabase
          .from('listings')
          .insert(testListings);

        if (insertError) throw insertError;

        // Refresh listings
        fetchListings();

        toast.success(`Created ${testListings.length} test listings with different statuses`);
      } else {
        toast.info(`Database already has ${count} listings`);
      }
    } catch (error) {
      console.error('Error creating test listings:', error);
      toast.error('Error creating test data');
    } finally {
      setIsCreatingTestData(false);
    }
  };

  // Function to force create test data (for debugging)
  const handleForceCreateTestData = () => {
    createTestListings(true);
  };

  return (
    <div className="min-h-screen flex flex-col">
      <Header />

      <main className="flex-grow">
        <Hero />

        <FeaturedListings />

        {/* Debug Section - Only for development */}
        {process.env.NODE_ENV === 'development' && (
          <section className="py-6 bg-gray-100">
            <div className="container mx-auto px-4 sm:px-6 lg:px-8 text-center">
              <h3 className="text-lg font-semibold mb-4 text-gray-700">
                Development Tools
              </h3>
              <Button
                onClick={handleForceCreateTestData}
                disabled={isCreatingTestData}
                variant="outline"
                size="sm"
              >
                {isCreatingTestData ? 'Creating...' : 'Force Create Test Data'}
              </Button>
              <p className="text-sm text-gray-500 mt-2">
                Creates test listings with different statuses for admin panel testing
              </p>
            </div>
          </section>
        )}

        {/* CTA Section */}
        <section className="py-12 bg-primary text-primary-foreground">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h2 className="text-3xl font-bold mb-6">
              {t('readyToSell')}
            </h2>
            <Button
              size="lg"
              variant="secondary"
              className="rounded-full px-8"
              asChild
            >
              <Link to="/create-listing">
                {t('createListing')}
              </Link>
            </Button>
          </div>
        </section>
      </main>

      <Footer />
    </div>
  );
};

export default Index;
