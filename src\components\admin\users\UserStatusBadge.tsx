
import React from 'react';
import { useLanguage } from '@/hooks/useLanguage';
import { Badge } from '@/components/ui/badge';

interface UserStatusBadgeProps {
  status: string;
}

const UserStatusBadge: React.FC<UserStatusBadgeProps> = ({ status }) => {
  const { t } = useLanguage();

  if (status === 'active') {
    return <Badge className="bg-green-500">{t('admin.active')}</Badge>;
  } else {
    return <Badge variant="outline">{t('admin.inactive')}</Badge>;
  }
};

export default UserStatusBadge;
