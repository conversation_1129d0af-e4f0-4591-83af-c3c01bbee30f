
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';
import { Listing } from '../types';

export const useUpdateListingStatus = (
  setIsLoading: React.Dispatch<React.SetStateAction<boolean>>,
  setListings: React.Dispatch<React.SetStateAction<Listing[]>>,
  setFeaturedListings: React.Dispatch<React.SetStateAction<Listing[]>>
) => {
  const setVipStatus = async (id: string, vipStatus: boolean): Promise<void> => {
    try {
      setIsLoading(true);

      // For now, we'll store these statuses client-side only
      // Long-term solution would be to add these columns to the database

      // Update the state directly without updating the database
      setListings(prevListings =>
        prevListings.map(item => item.id === id ? {...item, vip_status: vipStatus} : item)
      );

      setFeaturedListings(prevFeatured => {
        const idx = prevFeatured.findIndex(item => item.id === id);
        if (idx >= 0) {
          const newFeatured = [...prevFeatured];
          newFeatured[idx] = {...newFeatured[idx], vip_status: vipStatus};
          return newFeatured;
        }
        return prevFeatured;
      });

    } catch (error) {
      console.error('Error updating listing VIP status:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const setPaidStatus = async (id: string, paidStatus: boolean): Promise<void> => {
    try {
      setIsLoading(true);

      // For now, we'll store these statuses client-side only
      // Long-term solution would be to add these columns to the database

      // Update the state directly without updating the database
      setListings(prevListings =>
        prevListings.map(item => item.id === id ? {...item, paid_status: paidStatus} : item)
      );

      setFeaturedListings(prevFeatured => {
        const idx = prevFeatured.findIndex(item => item.id === id);
        if (idx >= 0) {
          const newFeatured = [...prevFeatured];
          newFeatured[idx] = {...newFeatured[idx], paid_status: paidStatus};
          return newFeatured;
        }
        return prevFeatured;
      });

    } catch (error) {
      console.error('Error updating listing paid status:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  // Add a function to update the listing status (pending, approved, rejected)
  const updateListingStatus = async (id: string, status: 'pending' | 'approved' | 'rejected'): Promise<void> => {
    try {
      setIsLoading(true);
      console.log(`Updating listing ${id} status to ${status}`);

      // Update the database first
      const { error } = await supabase
        .from('listings')
        .update({ status })
        .eq('id', id);

      if (error) {
        console.error('Database error updating listing status:', error);
        throw error;
      }

      console.log(`Successfully updated listing ${id} status to ${status} in database`);

      // Update the local state
      setListings(prevListings =>
        prevListings.map(item => item.id === id ? {...item, status} : item)
      );

      setFeaturedListings(prevFeatured => {
        const idx = prevFeatured.findIndex(item => item.id === id);
        if (idx >= 0) {
          const newFeatured = [...prevFeatured];
          newFeatured[idx] = {...newFeatured[idx], status};
          return newFeatured;
        }
        return prevFeatured;
      });

    } catch (error) {
      console.error('Error updating listing status:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  return { setVipStatus, setPaidStatus, updateListingStatus };
};
